import api from './api';
import { Group, WhatsAppGroup } from '../types/group';

/**
 * Obtém a lista de instâncias do WhatsApp
 */
export const getGroups = async (): Promise<Group[]> => {
    try {
        const response = await api.get<Group[]>('/instances');
        return response.data;
    } catch (error) {
        console.error('Error fetching WhatsApp instances:', error);
        throw error;
    }
};

/**
 * Obtém os detalhes de uma instância específica do WhatsApp
 */
export const getGroupDetails = async (instanceId: string): Promise<Group> => {
    try {
        const response = await api.get<Group>(`/instances/${instanceId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching instance details:', error);
        throw error;
    }
};

/**
 * Conecta uma instância do WhatsApp
 */
export const connectInstance = async (instanceId: string): Promise<void> => {
    try {
        await api.post(`/instances/${instanceId}/connect`);
    } catch (error) {
        console.error('Error connecting instance:', error);
        throw error;
    }
};

/**
 * Desconecta uma instância do WhatsApp
 */
export const disconnectInstance = async (instanceId: string): Promise<void> => {
    try {
        await api.post(`/instances/${instanceId}/disconnect`);
    } catch (error) {
        console.error('Error disconnecting instance:', error);
        throw error;
    }
};

/**
 * Reinicia uma instância do WhatsApp
 */
export const restartInstance = async (instanceId: string): Promise<void> => {
    try {
        await api.post(`/instances/${instanceId}/restart`);
    } catch (error) {
        console.error('Error restarting instance:', error);
        throw error;
    }
};

/**
 * Busca todos os grupos de uma instância do WhatsApp
 */
export const fetchInstanceGroups = async (instanceId: string): Promise<WhatsAppGroup[]> => {
    try {
        const response = await api.get<WhatsAppGroup[]>(`/instances/${instanceId}/groups`);
        return response.data;
    } catch (error) {
        console.error('Error fetching instance groups:', error);
        throw error;
    }
};

/**
 * Espelha um grupo do WhatsApp
 */
export const mirrorGroup = async (instanceId: string, groupId: string): Promise<void> => {
    try {
        await api.post(`/instances/${instanceId}/groups/${groupId}/mirror`);
    } catch (error) {
        console.error('Error mirroring group:', error);
        throw error;
    }
};