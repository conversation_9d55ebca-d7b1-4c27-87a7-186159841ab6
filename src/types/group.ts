export interface Group {
  id: string;
  name: string;
  connectionStatus: string;
  ownerJid: string;
  profileName: string;
  profilePicUrl: string;
  integration: string;
  number: string;
  businessId: string | null;
  token: string;
  clientName: string;
  disconnectionReasonCode: string | null;
  disconnectionObject: any | null;
  disconnectionAt: string | null;
  createdAt: string;
  updatedAt: string;
  Chatwoot: any | null;
  Proxy: any | null;
  Rabbitmq: any | null;
  Sqs: any | null;
  Websocket: any | null;
  Setting: {
    id: string;
    rejectCall: boolean;
    msgCall: string;
    groupsIgnore: boolean;
    alwaysOnline: boolean;
    readMessages: boolean;
    readStatus: boolean;
    syncFullHistory: boolean;
    wavoipToken: string;
    createdAt: string;
    updatedAt: string;
    instanceId: string;
  };
  _count: {
    Message: number;
    Contact: number;
    Chat: number;
  };
}

export interface WhatsAppInstance extends Group {
  // Campos específicos de instância, se houver
}

export interface WhatsAppGroup {
  id: string;
  name: string;
  participants: Participant[];
  createdAt?: string;
}

export interface Participant {
  id: string;
  name: string;
  isAdmin: boolean;
}

export interface MirrorGroup {
  sourceGroupId: string;
  targetGroupId?: string;
  includeHistory?: boolean;
}