/* Estilos para a página de listagem de instâncias */
.group-list {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.group-list h1 {
  color: #333;
  margin-bottom: 20px;
  font-size: 24px;
}

.group-list table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.group-list th {
  background-color: #f5f5f5;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #ddd;
}

.group-list td {
  padding: 12px;
  border-bottom: 1px solid #ddd;
}

.group-list tr:hover {
  background-color: #f9f9f9;
}

.group-list button {
  background-color: #4a90e2;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
  font-size: 14px;
}

.group-list button:hover {
  background-color: #3a80d2;
}

/* Estilos para a página de detalhes da instância */
.group-details {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.group-details h1 {
  color: #333;
  margin-bottom: 20px;
  font-size: 24px;
}

.group-details h2 {
  color: #555;
  margin-top: 30px;
  margin-bottom: 15px;
  font-size: 18px;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.actions {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

.actions button {
  background-color: #4a90e2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.actions button:hover {
  background-color: #3a80d2;
}

.actions button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.instance-details, .stats, .settings, .groups-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detail-row {
  display: flex;
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.detail-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.label {
  font-weight: 600;
  width: 150px;
  color: #555;
}

.value {
  flex: 1;
}

/* Estilos para a seção de grupos */
.groups-section {
  margin-top: 30px;
}

.groups-section h2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.refresh-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 15px;
}

.refresh-button:hover {
  background-color: #45a049;
}

.refresh-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.groups-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.groups-table th {
  background-color: #f5f5f5;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #ddd;
}

.groups-table td {
  padding: 12px;
  border-bottom: 1px solid #ddd;
}

.groups-table tr:hover {
  background-color: #f9f9f9;
}

.error-message {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}