import React, { useEffect, useState } from 'react';
import { getGroups } from '../../services/groupService';
import { Group } from '../../types/group';
import { useNavigate } from 'react-router-dom';
import './GroupStyles.css';

const GroupList = () => {
    const [groups, setGroups] = useState<Group[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const navigate = useNavigate();

    useEffect(() => {
        const fetchGroups = async () => {
            try {
                const data = await getGroups();
                setGroups(data);
            } catch (err) {
                setError('Failed to load groups');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchGroups();
    }, []);


    if (loading) return <div>Loading...</div>;
    if (error) return <div>{error}</div>;

    return (
        <div className="group-list">
            <h1>WhatsApp Instances</h1>
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Status</th>
                        <th>Number</th>
                        <th>Messages</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {groups.map((instance) => (
                        <tr key={instance.id}>
                            <td>{instance.name}</td>
                            <td>{instance.connectionStatus}</td>
                            <td>{instance.number}</td>
                            <td>{instance._count.Message}</td>
                            <td>
                                <button onClick={() => navigate(`/groups/${instance.id}`)}>
                                    Details
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default GroupList;