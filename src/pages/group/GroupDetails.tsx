import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
    getGroupDetails,
    connectInstance,
    disconnectInstance,
    restartInstance,
    fetchInstanceGroups,
    mirrorGroup
} from '../../services/groupService';
import { Group, WhatsAppGroup } from '../../types/group';
import './GroupStyles.css';

const GroupDetails = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [group, setGroup] = useState<Group | null>(null);
    const [groups, setGroups] = useState<WhatsAppGroup[]>([]);
    const [loading, setLoading] = useState(true);
    const [loadingGroups, setLoadingGroups] = useState(false);
    const [error, setError] = useState('');
    const [groupsError, setGroupsError] = useState('');

    const fetchGroups = async () => {
        if (!id) return;
        
        setLoadingGroups(true);
        try {
            const data = await fetchInstanceGroups(id);
            setGroups(data);
            setGroupsError('');
        } catch (err) {
            setGroupsError('Failed to load groups');
            console.error(err);
        } finally {
            setLoadingGroups(false);
        }
    };

    useEffect(() => {
        const fetchGroupDetails = async () => {
            if (!id) return;
            
            try {
                const data = await getGroupDetails(id);
                setGroup(data);
                
                // Buscar grupos automaticamente quando carregar os detalhes
                fetchGroups();
            } catch (err) {
                setError('Failed to load instance details');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchGroupDetails();
    }, [id]);

    const handleMirrorGroup = async (groupId: string) => {
        if (!id) return;
        
        try {
            await mirrorGroup(id, groupId);
            alert('Group mirrored successfully');
            
            // Recarregar a lista de grupos após o espelhamento
            fetchGroups();
        } catch (err) {
            alert('Failed to mirror group');
            console.error(err);
        }
    };

    if (loading) return <div>Loading...</div>;
    if (error) return <div>{error}</div>;
    if (!group) return <div>Group not found</div>;

    return (
        <div className="group-details">
            <h1>{group.name}</h1>
            <div className="actions">
                <button onClick={() => navigate(-1)}>Back to list</button>
                <button
                    onClick={async () => {
                        try {
                            await connectInstance(group.id);
                            alert('Instance connected successfully');
                        } catch (err) {
                            alert('Failed to connect instance');
                            console.error(err);
                        }
                    }}
                    disabled={group.connectionStatus === 'open'}
                >
                    Connect
                </button>
                <button
                    onClick={async () => {
                        try {
                            await disconnectInstance(group.id);
                            alert('Instance disconnected successfully');
                        } catch (err) {
                            alert('Failed to disconnect instance');
                            console.error(err);
                        }
                    }}
                    disabled={group.connectionStatus !== 'open'}
                >
                    Disconnect
                </button>
                <button
                    onClick={async () => {
                        try {
                            await restartInstance(group.id);
                            alert('Instance restarted successfully');
                        } catch (err) {
                            alert('Failed to restart instance');
                            console.error(err);
                        }
                    }}
                >
                    Restart
                </button>
            </div>
            
            <div className="groups-section">
                <h2>WhatsApp Groups</h2>
                <button
                    className="refresh-button"
                    onClick={fetchGroups}
                    disabled={loadingGroups}
                >
                    {loadingGroups ? 'Loading...' : 'Refresh Groups'}
                </button>
                
                {groupsError && <div className="error-message">{groupsError}</div>}
                
                {loadingGroups ? (
                    <div>Loading groups...</div>
                ) : groups.length > 0 ? (
                    <table className="groups-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Participants</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {groups.map((whatsappGroup) => (
                                <tr key={whatsappGroup.id}>
                                    <td>{whatsappGroup.name}</td>
                                    <td>{whatsappGroup.participants ? whatsappGroup.participants.length : 0}</td>
                                    <td>
                                        <button onClick={() => handleMirrorGroup(whatsappGroup.id)}>
                                            Mirror Group
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                ) : (
                    <div>No groups found</div>
                )}
            </div>
            
            <div className="instance-details">
                <h2>Instance Details</h2>
                <div className="detail-row">
                    <span className="label">Status:</span>
                    <span className="value">{group.connectionStatus}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Number:</span>
                    <span className="value">{group.number}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Profile Name:</span>
                    <span className="value">{group.profileName}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Integration:</span>
                    <span className="value">{group.integration}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Client Name:</span>
                    <span className="value">{group.clientName}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Created At:</span>
                    <span className="value">{new Date(group.createdAt).toLocaleString()}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Updated At:</span>
                    <span className="value">{new Date(group.updatedAt).toLocaleString()}</span>
                </div>
            </div>
            
            <div className="stats">
                <h2>Statistics</h2>
                <div className="detail-row">
                    <span className="label">Messages:</span>
                    <span className="value">{group._count.Message}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Contacts:</span>
                    <span className="value">{group._count.Contact}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Chats:</span>
                    <span className="value">{group._count.Chat}</span>
                </div>
            </div>
            
            <div className="settings">
                <h2>Settings</h2>
                <div className="detail-row">
                    <span className="label">Reject Call:</span>
                    <span className="value">{group.Setting.rejectCall ? 'Yes' : 'No'}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Groups Ignore:</span>
                    <span className="value">{group.Setting.groupsIgnore ? 'Yes' : 'No'}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Always Online:</span>
                    <span className="value">{group.Setting.alwaysOnline ? 'Yes' : 'No'}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Read Messages:</span>
                    <span className="value">{group.Setting.readMessages ? 'Yes' : 'No'}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Read Status:</span>
                    <span className="value">{group.Setting.readStatus ? 'Yes' : 'No'}</span>
                </div>
                <div className="detail-row">
                    <span className="label">Sync Full History:</span>
                    <span className="value">{group.Setting.syncFullHistory ? 'Yes' : 'No'}</span>
                </div>
            </div>
        </div>
    );
};

export default GroupDetails;