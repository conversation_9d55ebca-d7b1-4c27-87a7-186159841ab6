import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import GroupList from './pages/group/GroupList';
import GroupDetails from './pages/group/GroupDetails';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/groups" element={<GroupList />} />
        <Route path="/groups/:id" element={<GroupDetails />} />
      </Routes>
    </Router>
  );
}

export default App;