<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Variáveis de Ambiente</title>
</head>
<body>
    <h1>Debug Variáveis de Ambiente</h1>
    <div id="env-info"></div>

    <script type="module">
        // Verificar se as variáveis de ambiente estão sendo carregadas
        const envInfo = document.getElementById('env-info');
        
        const envVars = {
            'VITE_API_URL': import.meta.env.VITE_API_URL,
            'VITE_NODE_ENV': import.meta.env.VITE_NODE_ENV,
            'VITE_PASSWORD_DEV_USER': import.meta.env.VITE_PASSWORD_DEV_USER,
            'MODE': import.meta.env.MODE,
            'DEV': import.meta.env.DEV,
            'PROD': import.meta.env.PROD,
        };

        let html = '<h2>Variáveis de Ambiente:</h2><ul>';
        for (const [key, value] of Object.entries(envVars)) {
            html += `<li><strong>${key}:</strong> ${value || 'undefined'}</li>`;
        }
        html += '</ul>';

        html += '<h2>Todas as variáveis import.meta.env:</h2>';
        html += `<pre>${JSON.stringify(import.meta.env, null, 2)}</pre>`;

        envInfo.innerHTML = html;

        // Teste de fetch simples
        console.log('🔍 Testando variáveis de ambiente...');
        console.log('VITE_API_URL:', import.meta.env.VITE_API_URL);
        console.log('Todas as env vars:', import.meta.env);
    </script>
</body>
</html>
