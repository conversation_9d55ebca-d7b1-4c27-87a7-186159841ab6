<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Simples</title>
</head>
<body>
    <h1>Teste Básico de Conectividade</h1>
    <button onclick="testBasic()">Teste Básico</button>
    <div id="result"></div>

    <script>
        async function testBasic() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testando...';

            console.log('=== TESTE BÁSICO INICIADO ===');
            console.log('User Agent:', navigator.userAgent);
            console.log('Location:', window.location.href);

            // Teste 1: Verificar se fetch existe
            console.log('1. Fetch disponível:', typeof fetch !== 'undefined');

            // Teste 2: Testar localhost simples
            try {
                console.log('2. Testando localhost:3333...');
                const response = await fetch('http://localhost:3333/', {
                    method: 'GET',
                    mode: 'cors'
                });
                console.log('2. Resposta localhost:', response.status);
            } catch (error) {
                console.error('2. Erro localhost:', error);
            }

            // Teste 3: Testar 127.0.0.1
            try {
                console.log('3. Testando 127.0.0.1:3333...');
                const response = await fetch('http://127.0.0.1:3333/', {
                    method: 'GET',
                    mode: 'cors'
                });
                console.log('3. Resposta 127.0.0.1:', response.status);
            } catch (error) {
                console.error('3. Erro 127.0.0.1:', error);
            }

            // Teste 4: Testar sem CORS
            try {
                console.log('4. Testando sem CORS...');
                const response = await fetch('http://localhost:3333/', {
                    method: 'GET',
                    mode: 'no-cors'
                });
                console.log('4. Resposta no-cors:', response.status, response.type);
            } catch (error) {
                console.error('4. Erro no-cors:', error);
            }

            // Teste 5: Testar com XMLHttpRequest
            try {
                console.log('5. Testando XMLHttpRequest...');
                const xhr = new XMLHttpRequest();
                xhr.open('GET', 'http://localhost:3333/', true);
                xhr.onload = function() {
                    console.log('5. XHR Success:', xhr.status);
                };
                xhr.onerror = function() {
                    console.error('5. XHR Error');
                };
                xhr.send();
            } catch (error) {
                console.error('5. Erro XHR:', error);
            }

            // Teste 6: Verificar configurações de segurança
            console.log('6. Verificando configurações de segurança...');
            console.log('6. Location protocol:', window.location.protocol);
            console.log('6. Location hostname:', window.location.hostname);
            console.log('6. Location port:', window.location.port);
            console.log('6. Document domain:', document.domain);

            // Teste 7: Verificar se há Service Workers
            if ('serviceWorker' in navigator) {
                const registrations = await navigator.serviceWorker.getRegistrations();
                console.log('7. Service Workers:', registrations.length);
            }

            // Teste 8: Verificar headers de segurança
            try {
                console.log('8. Testando headers...');
                const response = await fetch('http://localhost:3333/', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                console.log('8. Headers da resposta:');
                for (let [key, value] of response.headers.entries()) {
                    console.log(`8.   ${key}: ${value}`);
                }
            } catch (error) {
                console.error('8. Erro ao verificar headers:', error);
            }

            resultDiv.innerHTML = 'Teste concluído. Verifique o console para detalhes.';
        }
    </script>
</body>
</html>
