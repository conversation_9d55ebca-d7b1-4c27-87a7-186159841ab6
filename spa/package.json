{"name": "spa-mirror", "private": true, "version": "0.2.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.62.8", "@tanstack/react-query-devtools": "^5.62.8", "@tanstack/react-table": "^8.20.6", "axios": "^1.7.9", "byte-size": "^9.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.15.0", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "next-themes": "^0.4.4", "nuqs": "^2.4.0", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-datepicker": "^7.5.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.1", "react-icons": "^5.4.0", "react-input-mask": "^2.0.4", "react-responsive": "^10.0.1", "react-router-dom": "^6.14.2", "react-select": "^5.9.0", "react-spinners": "^0.15.0", "react-toastify": "^11.0.1", "sonner": "^1.7.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/byte-size": "^8.1.2", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.13", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.1", "@types/react-input-mask": "^3.0.6", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "i": "^0.3.7", "npm": "^10.9.2", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}}