import { useCallback, useState } from "react";
import { AxiosResponse, AxiosRequestConfig, AxiosError } from "axios";
import { api } from "@/services/api";
import { toast } from "sonner";

type httpOptionsProps = {
  params?: object;
  silent?: boolean;
  header?: any;
  contentType?: "multipart" | "json";
  config?: AxiosRequestConfig;
};

export function useHttp() {
  const [isLoading, setIsLoading] = useState(false);

  const httpGet = useCallback(
    async <T = any,>(url: string, options?: httpOptionsProps): Promise<AxiosResponse<T>> => {
      const { params, silent = false } = options || {};

      try {
        api.defaults.headers["Content-Type"] = "application/json";
        if (!silent) setIsLoading(true);
        const response = await api.get(url, { params }).finally(() => {
          if (!silent) setIsLoading(false);
        });
        return response;
      } catch (err) {
        if (err instanceof AxiosError) {
          handleError(err);
          return err.response as AxiosResponse;
        }
        return {
          status: 500,
          data: { message: "Error, tente outra vez", err },
        } as AxiosResponse;
      }
    },
    []
  );

  const httpPost = useCallback(
    async <T = any,>(
      url: string,
      data: object,
      options?: httpOptionsProps
    ): Promise<AxiosResponse<T>> => {
      const { silent = false, contentType = "json", config } = options || {};

      try {
        api.defaults.headers["Content-Type"] =
          contentType === "json" ? "application/json" : "multipart/form-data";
        if (!silent) setIsLoading(true);
        const response = await api.post(url, data, config).finally(() => {
          if (!silent) setIsLoading(false);
        });
        return response;
      } catch (err) {
        if (err instanceof AxiosError) {
          handleError(err);
          return err.response as AxiosResponse;
        }
        return {
          status: 500,
          data: { message: "Error, tente outra vez", err },
        } as AxiosResponse;
      }
    },
    []
  );

  const httpPut = useCallback(
    async <T = any,>(
      url: string,
      data: object,
      options?: httpOptionsProps
    ): Promise<AxiosResponse<T>> => {
      const { silent = false, contentType = "json" } = options || {};

      try {
        api.defaults.headers["Content-Type"] =
          contentType === "json" ? "application/json" : "multipart/form-data";
        if (!silent) setIsLoading(true);
        const response = await api.put(url, data).finally(() => {
          if (!silent) setIsLoading(false);
        });
        return response;
      } catch (err) {
        if (err instanceof AxiosError) {
          handleError(err);
          return err.response as AxiosResponse;
        }
        return {
          status: 500,
          data: { message: "Error, tente outra vez", err },
        } as AxiosResponse;
      }
    },
    []
  );

  const httpDelete = useCallback(
    async <T = any,>(url: string, options?: httpOptionsProps): Promise<AxiosResponse<T>> => {
      const { params, silent = false } = options || {};

      try {
        api.defaults.headers["Content-Type"] = "application/json";
        if (!silent) setIsLoading(true);
        const response = await api.delete(url, { params }).finally(() => {
          if (!silent) setIsLoading(false);
        });
        return response;
      } catch (err) {
        if (err instanceof AxiosError) {
          handleError(err);
          return err.response as AxiosResponse;
        }
        return {
          status: 500,
          data: { message: "Error, tente outra vez", err },
        } as AxiosResponse;
      }
    },
    []
  );

  return { isLoading, httpGet, httpPost, httpPut, httpDelete };
}

type HandleError = AxiosError & {
  response?: {
    data: {
      code: string;
      message: string;
      status: number;
      statusCode: number;
      error?: string;
      errors?: string[];
    };
    status: number;
    statusText: string;
  };
};

export const isNetworkError = (err: AxiosError): boolean => !!err.isAxiosError && !err.response;

export function handleError(err: HandleError) {
  if (isNetworkError(err as AxiosError)) {
    toast.error("Você está sem acesso à internet ou o servidor está off-line.", {
      id: "networkError",
    });
    return;
  }

  const { response } = err;

  if (response?.status === 400 || response?.status === 422) {
    const { data } = response;

    if (data?.errors) {
      data.errors.forEach((item: any) => {
        toast.error(item.message);
      });
    } else if (data.message && typeof data.message === "string") {
      toast.error(data.message);
    } else if (data.message && Array.isArray(data.message)) {
      for (const msg of data.message) {
        if (typeof msg.error === "string") toast.error(msg.error);
      }
    }
    return;
  }

  if (response?.status === 401) {
    const { data } = response;
    if (data.message) {
      toast.error(data.message);
    } else {
      toast.info("Sua sessão expirou, ou você foi desconectado. Faça login outra vez.", {
        id: "sessionExpired",
      });
    }
    return;
  }

  if (response?.status === 403) {
    toast.error("Você tentou acessar um recurso o qual não possui permissão.");
    return;
  }

  if (response?.status === 413) {
    toast.error("Erro tamanho não suportado. Payload Too Large");
    return;
  }

  if (response?.status === 429) {
    const { data } = response;
    if (data.message) {
      toast.error(data.message);
    } else {
      toast.error("Atenção! Você está fazendo muitas requisições para o servidor.");
    }
    return;
  }

  if (response?.data) {
    const { data } = response;
    if (data.message) {
      toast.error(data.message);
    } else {
      toast.error("O servidor não conseguiu processar sua requisição.");
    }
    return;
  }

  toast.error("O servidor não conseguiu processar sua requisição.");
}
