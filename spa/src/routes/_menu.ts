import { MessageCircle, Settings2, Users, LayoutDashboard } from "lucide-react";
import { useStoreAuth } from "@/states/useStoreAuth";
import { MenuItem } from "@/types/menu-item";

const items: MenuItem[] = [
  {
    title: "Home",
    url: "/",
    icon: LayoutDashboard,
    isActive: false,
    can: [],
  },
   {
    title: "Painel",
    url: "/dashboard",
    icon: LayoutDashboard,
    isActive: false,
    can: [],
  },
  {
    title: "Contatos",
    url: "#",
    icon: Users,
    isActive: false,
    can: [],
    items: [
      {
        title: "Listar contatos",
        url: "/chats",
        can: [],
      },
      {
        title: "Grupos de clientes",
        url: "/groups-insurances",
        can: [],
      },
      {
        title: "Grupos de usuários",
        url: "/groups-users",
        can: [],
      },
    ],
  },
  {
    title: "Mensagens",
    url: "#",
    icon: MessageCircle,
    isActive: false,
    can: [],
    items: [
      {
        title: "Respostas positivas",
        url: "/chats/list-positive-messages",
        can: [],
      },
      {
        title: "Mensagens recebidas",
        url: "/chats/list-incoming-messages",
        can: [],
      },
      {
        title: "Mensagens enviadas",
        url: "/chats/list-sent-messages",
        can: [],
      },
    ],
  },
  {
    title: "Usuários",
    url: "/users",
    icon: Users,
    isActive: false,
    can: ["admin"],
  },
  {
    title: "Settings",
    url: "#",
    icon: Settings2,
    isActive: false,
    can: ["admin"],
    items: [
      {
        title: "Status Instance",
        url: "/settings/instance",
        can: [],
      },
      {
        title: "Salvar parâmetros",
        url: "/settings/params",
        can: [],
      },
      {
        title: "Banco de dados",
        url: "/settings/database",
        can: [],
      },
      {
        title: "Roles",
        url: "/settings/roles",
        can: [],
      },
      {
        title: "Permissions",
        url: "/settings/permissions",
        can: [],
      },
    ],
  },
];

export function useMenuItems() {
  const { user } = useStoreAuth();

  return items.filter((item) => {
    if (item.can.length === 0) return true;

    const hasPermission = ["admin", ...item.can].some((permission) =>
      user?.acl?.includes(permission)
    );

    if (!hasPermission) return false;

    if (item.items) {
      item.items = item.items.filter((subItem) => {
        if (subItem.can.length === 0) return true;
        return subItem.can.some((permission) => user?.acl?.includes(permission));
      });
    }

    return hasPermission || (item.items && item.items.length > 0);
  });
}
