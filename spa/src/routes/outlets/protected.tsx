import { Outlet, Navigate, useLocation } from "react-router-dom";
import { useStoreAuth } from "@/states/useStoreAuth";
import * as Layout from "@/components/layout";
import { ExpirationSessionAlert } from "@/components/expiration-session-alert";

export function Protected() {
  const location = useLocation();
  const { isAuthenticated } = useStoreAuth();

  if (!isAuthenticated && location.pathname !== "/login") {
    return <Navigate to="/login" />;
  }

  return (
    <Layout.Outlet>
      <Outlet />
      <ExpirationSessionAlert />
    </Layout.Outlet>
  );
}
