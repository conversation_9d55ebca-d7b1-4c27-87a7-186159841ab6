import { useEffect } from "react";
import { useRoutes, useLocation, useNavigate } from "react-router-dom";

import { SingIn } from "@/pages/auth/sing-in";
import { Forgot, NewPassword, ResetPassword } from "@/pages/auth";
import { Protected, ProtectedRoute } from "./outlets";
import { routes } from "./_routes";
import { useStoreAuth } from "@/states/useStoreAuth";
import { ErrorPage } from "@/pages/error-page";

export function Routes() {
  const nav = useNavigate();
  const { pathname } = useLocation();

  const { logout } = useStoreAuth();

  useEffect(() => {
    if (pathname === "/logout") {
      logout();
      nav("/login");
    }
  }, [pathname, nav, logout]);

  const elements = useRoutes([
    { path: "/login", element: <SingIn /> },
    { path: "/logout", element: <SingIn /> },
    { path: "/auth/forgot-password", element: <Forgot /> },
    { path: "/auth/reset/:token", element: <ResetPassword /> },
    { path: "/auth/new-password/:token", element: <NewPassword /> },
    // routes protected
    {
      path: "/",
      element: <Protected />,
      children: routes.map((route) => ({
        path: route.path,
        element: <ProtectedRoute can={route.can}>{route.element}</ProtectedRoute>,
      })),
    },
    { path: "*", element: <ErrorPage /> },
  ]);

  return elements;
}
