import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ser,
  FiPlus,
  FiTrash2,
  FiMail,
  FiExternalLink,
} from "react-icons/fi";
import {
  BsGear,
  BsFolder,
  BsFillBookmarkStarFill,
  BsBookmark,
  BsDownload,
  BsThreeDotsVertical,
  BsFileEarmarkArrowUp,
  BsFileEarmarkPlus,
  BsFiletypePdf,
  BsFileEarmarkWord,
  BsCalendarEvent,
  BsFileEarmark,
  BsChat,
  BsCheckSquare,
  BsSquare,
  BsCloudDownload,
} from "react-icons/bs";
import { GrDocumentText } from "react-icons/gr";
import { PiSignIn, PiSignOut, PiKey, PiPencilSimple } from "react-icons/pi";
import {
  MdOutlineDashboard,
  MdSearch,
  MdTune,
  MdOutlineCategory,
} from "react-icons/md";
import { FaRegAddressCard, FaWhatsapp } from "react-icons/fa";
import { GoBell } from "react-icons/go";
import { HiMenu } from "react-icons/hi";
import { HiMiniBuildingLibrary } from "react-icons/hi2";
import { SlLocationPin, SlTag } from "react-icons/sl";
import { GoLaw } from "react-icons/go";
import { RxExternalLink } from "react-icons/rx";
import { CiViewList } from "react-icons/ci";
import { TbFileDollar, TbLayoutList } from "react-icons/tb";
import { LuLayoutGrid } from "react-icons/lu";
import { IoPrintOutline } from "react-icons/io5";

export {
  FiUsers as UsersIcon,
  FiUser as UserIcon,
  BsGear as GearIcon,
  PiSignIn as SignInIcon,
  MdOutlineDashboard as DashboardIcon,
  PiSignOut as SignOutIcon,
  PiKey as KeyIcon,
  FaRegAddressCard as ProfileIcon,
  GoBell as BellIcon,
  HiMenu as MenuIcon,
  MdSearch as SearchIcon,
  MdTune as TuneIcon,
  FiPlus as PlusIcon,
  FiUsers as PeopleIcon,
  SlLocationPin as LocationIcon,
  BsFolder as FolderIcon,
  PiPencilSimple as PencilIcon,
  BsBookmark as BookmarkIcon,
  BsFillBookmarkStarFill as BookmarkFillIcon,
  BsThreeDotsVertical as DotsIcon,
  BsFileEarmarkArrowUp as FileUpIcon,
  BsFileEarmarkPlus as FilePlusIcon,
  BsFiletypePdf as PdfIcon,
  BsDownload as DownloadIcon,
  BsFileEarmarkWord as FileWordIcon,
  MdOutlineCategory as CategoryIcon,
  GoLaw as LawIcon,
  BsCalendarEvent as CalendarIcon,
  BsFileEarmark as FileIcon,
  RxExternalLink as LinkIcon,
  HiMiniBuildingLibrary as CourtIcon,
  BsChat as ChatIcon,
  CiViewList as ListIcon,
  SlTag as TagIcon,
  TbFileDollar as DollarIcon,
  FiTrash2 as TrashIcon,
  BsCheckSquare as CheckIcon,
  BsSquare as SquareIcon,
  BsCloudDownload as CloudDownloadIcon,
  FiMail as MailIcon,
  FiExternalLink as ExternalLinkIcon,
  TbLayoutList as LayoutListIcon,
  LuLayoutGrid as LayoutGridIcon,
  GrDocumentText as ReportIcon,
  IoPrintOutline as PrinterIcon,
  FaWhatsapp as WhatsAppIcon,
};
