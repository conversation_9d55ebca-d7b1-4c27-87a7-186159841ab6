import { useEffect } from "react";
import { useHttp } from "@/hooks";
import { z } from "zod";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQueryClient } from "@tanstack/react-query";
import { create } from "zustand";
import { Group, Options } from "@/types";

const formSchemaGroup = z.object({
  id: z.string().optional(),
  name: z.string().nonempty("Nome é obrigatório"),
  description: z.string().optional(),
  type: z.string().nonempty("Nome é obrigatório"),
});

const formSchemaChat = z.object({
  group_id: z.string(),
  chat_id: z.object(
    { label: z.string(), value: z.string() },
    { required_error: "selecione uma opção", invalid_type_error: "selecione uma opção válida" }
  ),
});

type FormSchemaGroup = z.infer<typeof formSchemaGroup>;
type FormSchemaChat = z.infer<typeof formSchemaChat>;

const resetForm = {
  name: "",
  description: "",
  type: "insurance",
};

type GroupStore = {
  openFormGroup: boolean;
  group: Group;
  chats: Options;
  setOpenFormGroup: (value: boolean) => void;
  setGroup: (value: Group) => void;
  setChats: (value: Options) => void;
};

const useGroupStore = create<GroupStore>((set) => ({
  openFormGroup: false,
  group: resetForm,
  chats: [],
  setOpenFormGroup: (value) => set({ openFormGroup: value }),
  setGroup: (value) => set({ group: value }),
  setChats: (value: Options) => set({ chats: value }),
}));

export function useGroup() {
  const { httpPost, httpPut } = useHttp();
  const queryClient = useQueryClient();
  const { openFormGroup, setOpenFormGroup, group, setGroup, chats, setChats } = useGroupStore();

  const methodsFormGroup = useForm<FormSchemaGroup>({
    resolver: zodResolver(formSchemaGroup),
    defaultValues: resetForm,
  });

  const methodsFormChat = useForm<FormSchemaChat>({
    resolver: zodResolver(formSchemaChat),
  });

  function resetFormGroup() {
    methodsFormGroup.reset(resetForm);
  }

  useEffect(() => {
    methodsFormGroup.reset(group);
  }, [group, methodsFormGroup]);

  async function onSubmitGroup(payload: FormSchemaGroup) {
    const { id, ...data } = payload;

    if (id) {
      const { status } = await httpPut(`/chats/groups/${id}`, data);
      if (status === 200) {
        queryClient.invalidateQueries({ queryKey: ["groups"] });
        toast.success("Grupo atualizado com sucesso!");
        return;
      }
    }

    const response = await httpPost<{ id: string }>(`/chats/groups`, data);
    if (response.status === 201) {
      methodsFormGroup.reset(response.data);
      toast.success("Grupo criado com sucesso!");
      setOpenFormGroup(false);
      queryClient.invalidateQueries({ queryKey: ["groups"] });
      return;
    }
  }
  async function onSubmitChat(payload: FormSchemaChat) {
    const { status } = await httpPost(`/chats/groups/chat`, {
      group_id: payload.group_id,
      chat_id: payload.chat_id.value,
    });
    if (status === 201) {
      queryClient.invalidateQueries({ queryKey: ["chat_group", payload.group_id] });
      toast.success("Contato adicionado com sucesso!");
      return;
    }
  }

  return {
    methodsFormGroup,
    methodsFormChat,
    onSubmitGroup,
    onSubmitChat,
    resetFormGroup,
    openFormGroup,
    setOpenFormGroup,
    setGroup,
    chats,
    setChats,
  };
}
