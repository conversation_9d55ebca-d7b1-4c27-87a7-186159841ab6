import { TrashConfirmButton } from "@/components/trash-confirm-button";
import { useHttp } from "@/hooks";
import { FaWhatsapp } from "react-icons/fa";
import { GroupChat } from "./card-group";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

interface CardChatProps {
  chat: GroupChat;
}
export function CardChat({ chat }: CardChatProps) {
  const { httpDelete } = useHttp();
  const queryClient = useQueryClient();

  async function handleDelete() {
    const { status } = await httpDelete(`/chats/groups/chat/${chat.group_chat_id}`);
    if (status === 200) {
      queryClient.invalidateQueries({ queryKey: ["chat_group", chat.group_id] });
      toast.success("Chat removido com sucesso");
    }
  }

  return (
    <div className="flex justify-between gap-2 border rounded-md p-2 bg-muted shadow-sm">
      <div className="flex items-center gap-2">
        <FaWhatsapp className="w-6 h-6 text-muted-foreground" />
        <div className="flex flex-col max-w-[14rem]">
          <span className="text-sm font-semibold truncate">{chat.name}</span>
          <span className="text-xs text-muted-foreground truncate">{chat.phone}</span>
        </div>
      </div>
      <TrashConfirmButton onAction={handleDelete} className="mr-1" />
    </div>
  );
}
