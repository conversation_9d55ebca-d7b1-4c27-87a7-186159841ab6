import * as Layout from "@/components/layout";

import DialogFormGroup from "./dialog-form-group";
import { CardGroup } from "./card-group";
import { useQuery } from "@tanstack/react-query";
import { Chat, Group } from "@/types";
import { useHttp } from "@/hooks";
import { useGroup } from "./useGroup";

export function GroupsInsurances() {
  const { httpGet } = useHttp();
  const { setChats } = useGroup();

  const { data } = useQuery<Group[]>({
    queryKey: ["groups"],
    queryFn: async () => {
      const { status, data } = await httpGet("/chats/groups/insurances");
      return status === 200 ? data : [];
    },
  });

  useQuery({
    queryKey: ["chats"],
    queryFn: async () => {
      const { status, data } = await httpGet<Chat[]>("/chats");
      if (status === 200) {
        setChats(
          data.map((chat) => ({
            label: `${chat.name} | ${chat.phone}`,
            value: chat.id,
          }))
        );
      }
      return [];
    },
  });

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Lista de Contatos", href: "/chats" },
          { name: "Listar Grupos de clientes" },
        ]}
      />
      <div className="grid grid-cols-12 gap-4 p-4">
        {data && data?.map((group) => <CardGroup key={group.id} group={group} />)}

        <DialogFormGroup />
      </div>
    </Layout.Root>
  );
}
