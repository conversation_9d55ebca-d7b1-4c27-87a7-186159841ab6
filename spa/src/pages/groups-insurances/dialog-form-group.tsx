import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input, InputHidden } from "@/components/form";
import { FormProvider } from "react-hook-form";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus } from "lucide-react";
import { useGroup } from "./useGroup";
import { Button } from "@/components/button";

export default function DialogFormGroup() {
  const { methodsFormGroup, onSubmitGroup, resetFormGroup, openFormGroup, setOpenFormGroup } =
    useGroup();
  const id = methodsFormGroup.watch("id");

  function handleOpen() {
    resetFormGroup();
    setOpenFormGroup(true);
  }

  return (
    <div className="col-span-12 md:col-span-4 flex items-center justify-center">
      <Dialog open={openFormGroup} onOpenChange={(value) => setOpenFormGroup(value)}>
        <DialogTrigger className="w-full h-full" onClick={handleOpen} asChild>
          <Card className="w-full h-full flex items-center justify-center hover:bg-muted cursor-pointer">
            <CardHeader>
              <CardTitle className="flex items-center justify-center gap-2">
                <Plus />
                Novo Grupo
              </CardTitle>
              <CardDescription>click para incluir um novo grupo</CardDescription>
            </CardHeader>
          </Card>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{id ? "Editar" : "Criar"} Grupo de Clientes</DialogTitle>
            <DialogDescription>
              os grupos são utilizados para reunir os contatos dos clientes para que seja possível
              direcionar as consultas de um usuário a uma determinada lista de clientes.
            </DialogDescription>
          </DialogHeader>
          <FormProvider {...methodsFormGroup}>
            <form
              onSubmit={methodsFormGroup.handleSubmit(onSubmitGroup)}
              className="grid h-fit w-full grid-cols-12 items-start gap-4"
            >
              <InputHidden name="id" />
              <Input label="Nome" name="name" className="col-span-12" />
              <Input label="Descrição" name="description" className="col-span-12" />

              <div className="col-span-full flex justify-end mt-4">
                <Button type="submit" isLoading={methodsFormGroup.formState.isSubmitting}>
                  Salvar
                </Button>
              </div>
            </form>
          </FormProvider>
        </DialogContent>
      </Dialog>
    </div>
  );
}
