import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";

import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { DialogFormChat } from "./dialog-form-chat";
import { Group } from "@/types";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useHttp } from "@/hooks";
import { CardChat } from "./card-chat";
import { EllipsisVertical, Pencil } from "lucide-react";
import { TrashConfirmButton } from "@/components/trash-confirm-button";
import { toast } from "sonner";
import { useGroup } from "./useGroup";

import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from "@/components/ui/menubar";

interface CardGroupProps {
  group: Group;
}

export interface GroupChat {
  group_chat_id: string;
  group_id: string;
  name: string;
  phone: string;
}

export function CardGroup({ group }: CardGroupProps) {
  const { httpGet, httpDelete } = useHttp();
  const { setOpenFormGroup, setGroup } = useGroup();
  const queryClient = useQueryClient();

  const { data } = useQuery<GroupChat[]>({
    queryKey: ["chat_group", group.id],
    queryFn: async () => {
      const { status, data } = await httpGet(`/chats/groups/get-chats-by-group/${group.id}`);
      return status === 200 ? data : [];
    },
  });

  async function handleDelete() {
    const { status } = await httpDelete(`/chats/groups/${group.id}`);
    if (status === 200) {
      queryClient.invalidateQueries({ queryKey: ["groups"] });
      toast.success("Grupo removido com sucesso");
    }
  }

  function handleEdit() {
    console.log({ group });
    setGroup(group);
    setOpenFormGroup(true);
  }

  return (
    <Card className="col-span-12 md:col-span-4 h-[calc(100vh-6rem)]">
      <CardHeader className="relative pl-10 h-[6rem]">
        <Menubar className="border-0 w-4 absolute left-4 top-7">
          <MenubarMenu>
            <MenubarTrigger className="w-1 h-6 p-1 flex items-center justify-center cursor-pointer">
              <EllipsisVertical className="h-6 w-6 shrink-0" />
            </MenubarTrigger>
            <MenubarContent>
              <MenubarItem onClick={handleEdit} className="text-foreground/70 cursor-pointer">
                <Pencil className="mr-2 h-4 w-4" />
                Editar
              </MenubarItem>
              <MenubarItem>
                <TrashConfirmButton onAction={handleDelete}>Remover</TrashConfirmButton>
              </MenubarItem>
            </MenubarContent>
          </MenubarMenu>
        </Menubar>

        <CardTitle className="text-2xl">{group.name}</CardTitle>
        <CardDescription>{group.description}</CardDescription>
        <DialogFormChat group={group} />
      </CardHeader>
      <CardContent>
        <ScrollArea className="flex  h-[calc(100vh-16rem)]  w-full p-2">
          <div className="flex flex-col gap-2">
            {data && data?.map((chat) => <CardChat key={chat.group_chat_id} chat={chat} />)}
          </div>
        </ScrollArea>
      </CardContent>
      <CardFooter>
        <CardDescription>{(data && data?.length) || 0} contatos no grupo</CardDescription>
      </CardFooter>
    </Card>
  );
}
