import { Options, User } from "@/types";
import { create } from "zustand";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useHttp } from "@/hooks";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { validateCpf } from "@/utils";

// STATE
type Store = {
  user: User | null;
  openForm: boolean;
  roles: Options;
  setUser: (value: User | null) => void;
  setOpenForm: (value: boolean) => void;
  setRoles: (value: Options) => void;
};

const useStore = create<Store>((set) => ({
  user: null,
  openForm: false,
  roles: [],
  acl: [],
  setUser: (value) => set({ user: value }),
  setOpenForm: (value) => set({ openForm: value }),
  setRoles: (value) => set({ roles: value }),
}));

// FORMS
const formSchema = z.object({
  id: z.string().optional(),
  first_name: z.string().nonempty("Nome é obrigatório"),
  last_name: z.string().nonempty("Sobrenome é obrigatório"),
  phone: z.string().nonempty("Número é obrigatório"),
  email: z.string().email("informe um email válido"),
  roles: z
    .object(
      { label: z.string(), value: z.string() },
      { required_error: "selecione uma opção", invalid_type_error: "selecione uma opção válida" }
    )
    .array()
    .optional(),
  status: z.boolean(),
  notify: z.boolean(),
  document: z
    .any()
    .refine(
      (cpf) => {
        if (!cpf) return false;
        return validateCpf(cpf);
      },
      { message: "informe um CPF válido" }
    )
    .transform((phone) => phone.replace(/\D/g, "")),
});

type FormSchema = z.infer<typeof formSchema>;

const resetForm = {
  id: "",
  first_name: "",
  last_name: "",
  phone: "",
  email: "",
  roles: [],
  acl: [],
  status: true,
  notify: false,
  document: "",
};

export function useUser() {
  const { httpPost, httpPut, httpDelete } = useHttp();
  const queryClient = useQueryClient();
  const { user, setUser, openForm, setOpenForm, roles, setRoles } = useStore();
  const methodsForm = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: resetForm,
  });

  useEffect(() => {
    methodsForm.reset({
      ...user,
      roles: user?.roles?.map((item) => ({ label: item.name, value: item.id! })),
      notify: user?.notify || false,
      status: user?.status || false,
    });
  }, [methodsForm, user]);

  async function onSubmit(payload: FormSchema) {
    const { id, ...data } = payload;
    if (id) {
      const { status } = await httpPut(`/users/${id}`, {
        ...data,
        roles: data.roles ? data.roles.map((item) => item.value) : null,
      });
      if (status === 200) {
        toast.success("Usuário atualizado com sucesso!");
      }
    }
    if (!id) {
      const { status } = await httpPost("/users", {
        ...data,
        roles: data.roles ? data?.roles.map((item) => item.value) : null,
      });
      if (status === 201) {
        toast.success("Usuário criado com sucesso!");
        setOpenForm(false);
      }
    }

    queryClient.invalidateQueries({ queryKey: ["users"] });
  }

  async function onDelete(id: string) {
    const { status } = await httpDelete(`/users/${id}`);
    if (status === 200) {
      toast.success("Usuário deletado com sucesso!");
      setOpenForm(false);
    }
    queryClient.invalidateQueries({ queryKey: ["users"] });
  }

  return {
    openForm,
    setOpenForm,
    roles,
    setRoles,
    user,
    setUser,
    methodsForm,
    onSubmit,
    onDelete,
    resetForm,
  };
}
