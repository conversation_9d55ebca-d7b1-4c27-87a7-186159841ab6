import * as Layout from "@/components/layout";
import { useHttp } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { Input } from "@/components/form";
import { toast } from "sonner";
import { Button } from "@/components/button";


const strongPasswordRegex =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,}$/;

const formSchema = z
  .object({
    password: z
      .string()
      .regex(
        strongPasswordRegex,
        "Senha fraca! Combine letras maiúsculas, minúsculas, números e símbolos"
      )
      .min(8, { message: "senha é obrigatória, 8 carácteres mínimos" }),
    password_confirm: z.string(),
  })
  .refine((data) => data.password === data.password_confirm, {
    message: "as senhas não conferem",
    path: ["password_confirm"],
  });

type FormSchema = z.infer<typeof formSchema>;

export function ChangePassword() {

  const methods = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      password_confirm: "",
    },
  });

  const { httpPut } = useHttp();

  async function onSubmit(payload: FormSchema) {
    const { status } = await httpPut(`/users/change-password`, payload);
    if (status === 200) {
      methods.reset();
      toast.success("Senha alterada com sucesso!");
      return;
    }

    toast.error("Erro ao alterar senha");
    return;
  }

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Meus dados", href: "/profile" },
          { name: "Alterar senha", href: "/reset-password" },
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0 max-w-[400px]">
        <div>
          <FormProvider {...methods}>
            <form
              onSubmit={methods.handleSubmit(onSubmit)}
              className="grid h-fit w-full grid-cols-12 items-start gap-4"
            >
              <Input label="Nova senha" name="password" type="password" />
              <Input label="Confirme a nova senha" name="password_confirm" type="password" />

              <div className="col-span-full flex justify-end">
                <Button type="submit" isLoading={methods.formState.isSubmitting}>
                  Enviar
                </Button>
              </div>
            </form>
          </FormProvider>
        </div>
      </div>
    </Layout.Root>
  );
}
