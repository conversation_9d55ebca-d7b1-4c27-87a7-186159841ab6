import * as Layout from "@/components/layout";
import { useHttp } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { Input } from "@/components/form";
import { toast } from "sonner";
import { useStoreAuth } from "@/states/useStoreAuth";
import { Button } from "@/components/button";
import { validateCpf } from "@/utils";

const formSchema = z.object({
  first_name: z.string().nonempty("Nome é obrigatório"),
  last_name: z.string().nonempty("Sobrenome é obrigatório"),
  document: z
    .string({
      invalid_type_error: "informe um CPF válido",
      required_error: "informe um CPF válido",
    })
    .transform((document) => document.replace(/\D/g, "")),
  phone: z.string().nonempty("Número é obrigatório"),
  email: z.string().email("informe um email válido"),
}).refine(
  (data) => {
    if (!data.document) return true;
    return validateCpf(data.document);
  },
  { message: "informe um número válido", path: ["document"] },
);

type FormSchema = z.infer<typeof formSchema>;

export function Profile() {
  const setUser = useStoreAuth((s) => s.setUser);
  const user = useStoreAuth((s) => s.user);

  const methods = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      first_name: user?.first_name,
      last_name: user?.last_name,
      document: user?.document,
      phone: user?.phone,
      email: user?.email,
    },
  });

  const { httpPut } = useHttp();

  async function onSubmit(payload: FormSchema) {
    const { status, data } = await httpPut(`/users/profile`, payload);
    if (status === 200) {
      setUser(data);
      toast.success("Meus dados foram atualizado com sucesso!");
      return;
    }

    toast.error("Erro ao atualizar os meu dados");
    return;
  }

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Meus dados", href: "/users" },
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0 max-w-[600px]">
        <div>
          <FormProvider {...methods}>
            <form
              onSubmit={methods.handleSubmit(onSubmit)}
              className="grid h-fit w-full grid-cols-12 items-start gap-4"
            >
              <Input label="Nome" name="first_name" className="col-span-12 md:col-span-4" />
              <Input label="Sobrenome" name="last_name" className="col-span-12 md:col-span-8" />

              <Input
                label="CPF"
                name="document"
                className="col-span-12 md:col-span-6"
              />


              <Input label="Número (5521xxxxxxxxx)" name="phone" className="col-span-12 md:col-span-6" />
              <Input label="email" name="email" className="col-span-12" />

              <div className="col-span-full flex justify-end">
                <Button type="submit" isLoading={methods.formState.isSubmitting}>
                  Enviar
                </Button>
              </div>
            </form>
          </FormProvider>
        </div>
      </div>
    </Layout.Root>
  );
}
