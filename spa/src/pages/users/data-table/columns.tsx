import { User } from "@/types";
import { ColumnDef } from "@tanstack/react-table";
import { CopyToClipboard } from "@/components/copy-to-clipboard";
import { Button } from "@/components/ui/button";
import { ArrowUpDown } from "lucide-react";
import { Badge } from "@/components/ui/badge";

export const columns: ColumnDef<User>[] = [
  {
    accessorKey: "name",
    header: "Nome",
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          <CopyToClipboard>
            {row.original.first_name} {row.original.last_name}
          </CopyToClipboard>
        </div>
      );
    },
  },
  {
    accessorKey: "phone",
    header: "WhatsApp",
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          <CopyToClipboard>{row.original.phone}</CopyToClipboard>
        </div>
      );
    },
  },
  {
    accessorKey: "email",
    header: "E-mail",
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          <CopyToClipboard>{row.original.email}</CopyToClipboard>
        </div>
      );
    },
  },
  {
    accessorKey: "document",
    header: "CPF",
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          <CopyToClipboard>{row.original.document}</CopyToClipboard>
        </div>
      );
    },
  },
  {
    accessorKey: "roles",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Cargo
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          {row.original.roles?.map((role) => role.name).join(", ")}
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          {row.original.status ? (
            <Badge variant="outline">Ativo</Badge>
          ) : (
            <Badge variant="secondary">Inativo</Badge>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "notify",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Notificar
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          {row.original.notify ? (
            <Badge variant="outline">Ativo</Badge>
          ) : (
            <Badge variant="secondary">Inativo</Badge>
          )}
        </div>
      );
    },
  },
];
