import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { Input, InputHidden, InputMask, Select } from "@/components/form";
import * as Check from "@/components/form/input-checkbox";
import { FormProvider } from "react-hook-form";

import { Plus } from "lucide-react";
import { useUser } from "./useUser";
import { Button } from "@/components/button";
import { TrashConfirmButton } from "@/components/trash-confirm-button";
import { useStoreAuth } from "@/states/useStoreAuth";

export default function DialogForm() {
  const { can } = useStoreAuth();
  const { methodsForm, onSubmit, onDelete, openForm, setOpenForm, setUser, resetForm, roles } =
    useUser();
  const id = methodsForm.watch("id");

  function handleOpen() {
    setUser(resetForm);
    setOpenForm(true);
  }

  if (!can("c-user")) return null;

  return (
    <div className="col-span-12 md:col-span-4 flex items-center justify-center">
      <Dialog open={openForm} onOpenChange={(value) => setOpenForm(value)}>
        <DialogTrigger className="w-full h-full" onClick={handleOpen} asChild>
          <Button variant="outline" className="h-9">
            <Plus className="w-4 h-4" /> Novo Usuário
          </Button>
        </DialogTrigger>
        <DialogContent className="w-[800px] max-w-[90%]">
          <DialogHeader>
            <DialogTitle>{id ? "Editar" : "Criar"} Usuário</DialogTitle>
          </DialogHeader>
          <FormProvider {...methodsForm}>
            <form
              onSubmit={methodsForm.handleSubmit(onSubmit)}
              className="grid h-fit w-full grid-cols-12 items-start gap-4"
            >
              <InputHidden name="id" />
              <Input label="Nome" name="first_name" className="col-span-12 md:col-span-4" />
              <Input label="Sobrenome" name="last_name" className="col-span-12 md:col-span-8" />
              <Input label="Número (5521xxxxxxxxx)" name="phone" className="col-span-4" />
              <Input label="E-mail" name="email" className="col-span-8" />
              <InputMask
                label="CPF"
                name="document"
                mask="999.999.999-99"
                className="col-span-12 md:col-span-4"
              />
              <Select
                label="Cargo"
                name="roles"
                isMulti
                className="col-span-12 md:col-span-8"
                options={roles}
              />
              <div className="col-span-2 flex items-center pt-1">
                <Check.Control name="status" label="Status" />
              </div>
              <div className="col-span-10 flex items-center pt-1">
                <Check.Control name="notify" label="Notificar" />
              </div>
              <div className="col-span-full flex justify-between">
                {id && (
                  <TrashConfirmButton onAction={() => onDelete(id)}>
                    Excluir este usuário
                  </TrashConfirmButton>
                )}
                <div className="mx-auto" />
                <Button type="submit" isLoading={methodsForm.formState.isSubmitting}>
                  Salvar
                </Button>
              </div>
            </form>
          </FormProvider>
        </DialogContent>
      </Dialog>
    </div>
  );
}
