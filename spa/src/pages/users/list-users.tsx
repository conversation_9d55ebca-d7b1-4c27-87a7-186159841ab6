import * as Layout from "@/components/layout";
import { useHttp } from "@/hooks";
import { DataTable } from "./data-table/data-table";
import { columns } from "./data-table/columns";
import { useQuery } from "@tanstack/react-query";
import { Role, User } from "@/types";
import { useUser } from "./useUser";

export function ListUsers() {
  const { setRoles, setUser, setOpenForm } = useUser();
  const { httpGet } = useHttp();

  const { data, isLoading } = useQuery({
    queryKey: ["users"],
    queryFn: async () => {
      const { data } = await httpGet<User[]>("/users");
      return data ? data : [];
    },
  });

  useQuery({
    queryKey: ["roles"],
    queryFn: async () => {
      const { data } = await httpGet<Role[]>("/auth/roles");
      setRoles(
        data.map((group) => ({
          label: group.name,
          value: group.id!,
        }))
      );
      return [];
    },
  });

  function handleRowClick(row: User) {
    setUser(row);
    setOpenForm(true);
  }

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Lista de Usuários", href: "/users" },
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="flex flex-col gap-2">
          <h1>Lista de Usuários</h1>
          <DataTable
            columns={columns}
            data={data!}
            isLoading={isLoading}
            handleRowClick={(row) => handleRowClick(row.original)}
          />
        </div>
      </div>
    </Layout.Root>
  );
}
