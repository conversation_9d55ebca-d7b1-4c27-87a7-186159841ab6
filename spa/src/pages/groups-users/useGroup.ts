import { useHttp } from "@/hooks";
import { z } from "zod";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQueryClient } from "@tanstack/react-query";
import { create } from "zustand";
import { useEffect } from "react";
import { Group, Options } from "@/types";

// STATE
type GroupStore = {
  openFormGroup: boolean;
  group: Group;
  chats: Options;
  groupsInsurance: Options;
  setOpenFormGroup: (value: boolean) => void;
  setGroup: (value: Group) => void;
  setChats: (value: Options) => void;
  setGroupsInsurance: (value: Options) => void;
};

const useGroupStore = create<GroupStore>((set) => ({
  openFormGroup: false,
  group: {} as Group,
  chats: [],
  groupsInsurance: [],
  setOpenFormGroup: (value) => set({ openFormGroup: value }),
  setGroup: (value) => set({ group: value }),
  setChats: (value) => set({ chats: value }),
  setGroupsInsurance: (value) => set({ groupsInsurance: value }),
}));

// FORMS
const formSchemaGroup = z.object({
  id: z.string().optional(),
  name: z.string().nonempty("nome é obrigatório"),
  description: z.string().optional(),
  send_to_group_id: z.object(
    { label: z.string(), value: z.string() },
    { required_error: "selecione uma opção", invalid_type_error: "selecione uma opção válida" }
  ),
  type: z.string().nonempty("nome é obrigatório"),
});

const formSchemaChat = z.object({
  group_id: z.string(),
  chat_id: z.object(
    { label: z.string(), value: z.string() },
    { required_error: "selecione uma opção", invalid_type_error: "selecione uma opção válida" }
  ),
});

type FormSchemaGroup = z.infer<typeof formSchemaGroup>;
type FormSchemaChat = z.infer<typeof formSchemaChat>;

const resetForm = {
  name: "",
  description: "",
  send_to_group_id: { label: "", value: "" },
  type: "user",
};

export function useGroup() {
  const { httpPost, httpPut } = useHttp();
  const queryClient = useQueryClient();
  const {
    openFormGroup,
    setOpenFormGroup,
    group,
    setGroup,
    chats,
    setChats,
    groupsInsurance,
    setGroupsInsurance,
  } = useGroupStore();

  const methodsFormGroup = useForm<FormSchemaGroup>({
    resolver: zodResolver(formSchemaGroup),
    defaultValues: resetForm,
  });

  const methodsFormChat = useForm<FormSchemaChat>({
    resolver: zodResolver(formSchemaChat),
  });

  function resetFormGroup() {
    methodsFormGroup.reset(resetForm);
  }

  useEffect(() => {
    methodsFormGroup.reset({
      ...group,
      send_to_group_id: { label: group.SendToGroup?.name, value: group.SendToGroup?.id },
    });
  }, [group, methodsFormGroup]);

  async function onSubmitGroup(payload: FormSchemaGroup) {
    const { id, ...data } = payload;

    if (id) {
      const { status } = await httpPut(`/chats/groups/${id}`, {
        ...data,
        send_to_group_id: data.send_to_group_id.value,
      });
      if (status === 200) {
        queryClient.invalidateQueries({ queryKey: ["groups"] });
        toast.success("Grupo atualizado com sucesso!");
        return;
      }
    }

    const response = await httpPost<Group>(`/chats/groups`, {
      ...data,
      send_to_group_id: data.send_to_group_id.value,
    });
    if (response.status === 201) {
      const { data } = response;
      methodsFormGroup.reset({
        id: data.id,
        name: data.name,
        description: data.description,
        send_to_group_id: { label: data.SendToGroup?.name, value: data.SendToGroup?.id },
      });
      toast.success("Grupo criado com sucesso!");
      setOpenFormGroup(false);
      queryClient.invalidateQueries({ queryKey: ["groups", "users"] });
      return;
    }
  }

  async function onSubmitChat(payload: FormSchemaChat) {
    const { status } = await httpPost(`/chats/groups/chat`, {
      group_id: payload.group_id,
      chat_id: payload.chat_id.value,
      is_unique: true,
    });
    if (status === 201) {
      queryClient.invalidateQueries({ queryKey: ["chat_group", payload.group_id] });
      toast.success("Contato adicionado com sucesso!");
      return;
    }
  }

  return {
    methodsFormGroup,
    methodsFormChat,
    onSubmitGroup,
    onSubmitChat,
    resetFormGroup,
    openFormGroup,
    setOpenFormGroup,
    setGroup,
    chats,
    groupsInsurance,
    setGroupsInsurance,
    setChats,
  };
}
