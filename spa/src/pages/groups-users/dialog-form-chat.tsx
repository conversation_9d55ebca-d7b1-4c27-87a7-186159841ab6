import { Toolt<PERSON>, Toolt<PERSON>Content, Too<PERSON><PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/button";
import { Plus } from "lucide-react";
import { FormProvider } from "react-hook-form";
import { InputHidden, Select } from "@/components/form";
import { useGroup } from "./useGroup";
import { Group } from "@/types";

interface DialogFormChatProps {
  group: Group | undefined;
}

export function DialogFormChat({ group }: DialogFormChatProps) {
  const { methodsFormChat, onSubmitChat, chats } = useGroup();

  return (
    <div>
      <Dialog>
        <DialogTrigger>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger className="absolute right-4 top-4">
                <Button variant="outline" size="icon">
                  <Plus />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Click aqui para adicionar um contato</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adicionar contatos ao grupo {group?.name}</DialogTitle>
            <DialogDescription>
              você pode manter a janela aberta enquanto vai adicionando vários contatos.
            </DialogDescription>
          </DialogHeader>
          <FormProvider {...methodsFormChat}>
            <form
              onSubmit={methodsFormChat.handleSubmit(onSubmitChat)}
              className="grid h-fit w-full grid-cols-12 items-start gap-4"
            >
              <InputHidden name="group_id" value={group?.id} />
              <Select name="chat_id" label="Chat" options={chats} />
              <div className="col-span-full flex justify-end mt-4">
                <Button type="submit" isLoading={methodsFormChat.formState.isSubmitting}>
                  Salvar
                </Button>
              </div>
            </form>
          </FormProvider>
        </DialogContent>
      </Dialog>
    </div>
  );
}
