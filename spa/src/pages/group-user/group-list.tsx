import React from 'react';
import { useQuery } from 'react-query';
import { getGroups } from '../../services/groupService';
import { Group } from '../../types/group';

const GroupList = () => {
    const { data: groups, isLoading } = useQuery('groups', getGroups);

    if (isLoading) return <div>Loading...</div>;

    return (
        <div>
            <h1>Group List</h1>
            <ul>
                {groups?.map((group: Group) => (
                    <li key={group.id}>
                        {group.name} - {group.participants.length} participants
                    </li>
                ))}
            </ul>
        </div>
    );
};

export default GroupList;