import * as Layout from "@/components/layout";
import { useQuery } from "@tanstack/react-query";
import { Role } from "@/types";
import { useHttp } from "@/hooks";
import { DataTable } from "./data-table/data-table";
import { columns } from "./data-table/columns";
import { useRole } from "./useRole";

export function Roles() {
  const { httpGet } = useHttp();
  const { setOpenForm, setRole } = useRole();

  const { data, isLoading } = useQuery<Role[]>({
    queryKey: ["roles"],
    queryFn: async () => {
      const { status, data } = await httpGet("/auth/roles");
      return status === 200 ? data : [];
    },
  });

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Settings", href: "#" },
          { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/settings/permissions" },
          { name: "Cargos", href: "#" },
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div>
          <h1>Cargos</h1>

          <DataTable
            columns={columns}
            data={data || []}
            isLoading={isLoading}
            handleRowClick={(row) => {
              setRole(row.original);
              setOpenForm(true);
            }}
          />
        </div>
      </div>
    </Layout.Root>
  );
}
