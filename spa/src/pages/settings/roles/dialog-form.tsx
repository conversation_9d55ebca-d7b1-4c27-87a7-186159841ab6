import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input, InputHidden } from "@/components/form";
import * as Check from "@/components/form/input-checkbox";
import { FormProvider } from "react-hook-form";

import { Plus } from "lucide-react";
import { useRole } from "./useRole";
import { Button } from "@/components/button";

export default function DialogForm() {
  const { methodsForm, onSubmit, openForm, setOpenForm, setRole, resetForm } = useRole();
  const id = methodsForm.watch("id");

  function handleOpen() {
    setRole(resetForm);
    setOpenForm(true);
  }

  return (
    <div className="col-span-12 md:col-span-4 flex items-center justify-center">
      <Dialog open={openForm} onOpenChange={(value) => setOpenForm(value)}>
        <DialogTrigger className="w-full h-full" onClick={handleOpen} asChild>
          <Button variant="outline" className="h-9">
            <Plus className="w-4 h-4" /> Novo cargo
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{id ? "Editar" : "Criar"} Cargo</DialogTitle>
            <DialogDescription>
              um cargo é um conjunto de permissões que podem ser atribuídas a um usuário.
            </DialogDescription>
          </DialogHeader>
          <FormProvider {...methodsForm}>
            <form
              onSubmit={methodsForm.handleSubmit(onSubmit)}
              className="grid h-fit w-full grid-cols-12 items-start gap-4"
            >
              <InputHidden name="id" />
              <Input label="Nome" name="name" className="col-span-12" />
              <Input label="Slug" name="slug" className="col-span-12" />
              <Input label="Descrição" name="description" className="col-span-12" />
              <div className="col-span-10 flex items-center pt-1">
                <Check.Control name="status" label="Status" />
              </div>
              <div className="col-span-full flex justify-end mt-4">
                <Button type="submit" isLoading={methodsForm.formState.isSubmitting}>
                  Salvar
                </Button>
              </div>
            </form>
          </FormProvider>
        </DialogContent>
      </Dialog>
    </div>
  );
}
