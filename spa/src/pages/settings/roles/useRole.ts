import { Role } from "@/types";
import { create } from "zustand";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useHttp } from "@/hooks";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

// STATE
type RoleStore = {
  openForm: boolean;
  role: Role;
  setOpenForm: (value: boolean) => void;
  setRole: (value: Role) => void;
};

const useStore = create<RoleStore>((set) => ({
  openForm: false,
  role: {} as Role,
  setOpenForm: (value) => set({ openForm: value }),
  setRole: (value) => set({ role: value }),
}));

// FORMS
const formSchema = z.object({
  id: z.string().optional(),
  name: z.string().nonempty("nome é obrigatório"),
  slug: z.string().nonempty("slug é obrigatório"),
  description: z.string().optional(),
  status: z.boolean(),
});

type FormSchema = z.infer<typeof formSchema>;

const resetForm = {
  name: "",
  slug: "",
  description: "",
  status: true,
};

export function useRole() {
  const { httpPost, httpPut } = useHttp();
  const queryClient = useQueryClient();
  const { openForm, setOpenForm, role, setRole } = useStore();
  const methodsForm = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: resetForm,
  });

  useEffect(() => {
    methodsForm.reset({ ...role, description: role.description || "" });
  }, [methodsForm, role]);

  async function onSubmit(payload: FormSchema) {
    const { id, ...data } = payload;
    if (id) {
      const { status } = await httpPut(`/auth/roles/${id}`, data);
      if (status === 200) {
        toast.success("Cargo atualizado com sucesso!");
        setOpenForm(false);
      }
    }
    if (!id) {
      const { status } = await httpPost("/auth/roles", data);
      if (status === 201) {
        toast.success("Cargo criado com sucesso!");
        setOpenForm(false);
      }
    }

    queryClient.invalidateQueries({ queryKey: ["roles"] });
  }

  return {
    openForm,
    setOpenForm,
    role,
    setRole,
    methodsForm,
    onSubmit,
    resetForm,
  };
}
