import { Role } from "@/types";
import { ColumnDef } from "@tanstack/react-table";
import { CopyToClipboard } from "@/components/copy-to-clipboard";

export const columns: ColumnDef<Role>[] = [
  {
    accessorKey: "name",
    header: "Nome",
    cell: ({ row }) => {
      return <div className="flex items-center">{row.original.name}</div>;
    },
  },
  {
    accessorKey: "slug",
    header: "Slug",
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          <CopyToClipboard>{row.original.slug}</CopyToClipboard>
        </div>
      );
    },
  },
  {
    accessorKey: "description",
    header: "Descrição",
    cell: ({ row }) => {
      return <div className="flex items-center"> {row.original.description} </div>;
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      return <div className="flex items-center"> {row.original.status ? "Ativo" : "Inativo"} </div>;
    },
  },
];
