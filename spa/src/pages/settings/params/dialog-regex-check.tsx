
import { Button } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useHttp } from "@/hooks"
import { useRef } from "react"
import { toast } from "sonner"

export function DialogRegexCheck() {
    const inputRef = useRef<HTMLTextAreaElement>(null)
    const { httpPost } = useHttp()



    async function handleCheckRegex() {
        const message = inputRef.current?.value;
        const { status, data } = await httpPost('/chats/dialogues/regex-check', { message });
        if (status === 201) {
            toast.success(data.message);
            return;
        }
    }


    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="outline">Validar Regex</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Validar Regex</DialogTitle>
                    <DialogDescription>
                        escreva uma mensagem simulando uma resposta do cliente e teste se o Regex salvo está funcionando.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-4 items-center gap-4">
                    <div className="grid col-span-full w-full max-w-sm items-center gap-1.5">
                        <Label htmlFor="email">Mensagem</Label>
                        <Textarea placeholder="digite a mensagem" ref={inputRef} />
                    </div>
                </div>
                <DialogFooter>
                    <Button type="button" onClick={handleCheckRegex}>Enviar</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
