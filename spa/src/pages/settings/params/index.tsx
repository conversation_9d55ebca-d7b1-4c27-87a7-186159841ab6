import * as Layout from "@/components/layout";
import { useHttp } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { Input, InputArea } from "@/components/form";
import { But<PERSON> } from "@/components/button";
import { toast } from "sonner";
import { DialogRegexCheck } from "./dialog-regex-check";
import { useQuery, useQueryClient } from "@tanstack/react-query";

const formSchema = z.object({
  id: z.string(),
  client_token: z.string().nonempty("campo obrigatório"),
  instance_id: z.string().nonempty("campo obrigatório"),
  instance_token: z.string().nonempty("campo obrigatório"),
  match_positive_regex: z.string().nonempty("campo obrigatório"),
  match_ignore_regex: z.string().nonempty("campo obrigatório"),
  unique_interval: z.coerce.number().min(1, "intervalo mínimo de 1 minuto"),
});

type FormSchema = z.infer<typeof formSchema>;

export function Params() {
  const queryClient = useQueryClient();
  const methods = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
  });

  const { httpGet, httpPut } = useHttp();

  useQuery({
    queryKey: ["settings"],
    queryFn: async () => {
      const { data } = await httpGet("/chats/settings/params");
      methods.reset(data);
      return data;
    },
  });

  async function onSubmit(payload: FormSchema) {
    const { status } = await httpPut(`/chats/settings/params`, payload);
    if (status === 200) {
      toast.success("Parâmetros atualizado com sucesso!");
      queryClient.invalidateQueries({ queryKey: ["settings"] });
      return;
    }

    toast.error("Erro ao atualizar o parâmetros");
    return;
  }

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Settings", href: "#" },
          { name: "Parâmetros", href: "#" },
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0 max-w-[1080px]">
        <div>
          <FormProvider {...methods}>
            <form
              onSubmit={methods.handleSubmit(onSubmit)}
              className="grid h-fit w-full grid-cols-12 items-start gap-4"
            >
              <Input label="instance_token" name="instance_token" className="col-span-6" />
              <Input
                label="Intervalo (minutos) de verificação para uma Placa/Chassi já consultada"
                name="unique_interval"
                className="col-span-6"
                type="number"
                step={1}
              />
              <Input label="client_token" name="client_token" className="col-span-6" />
              <Input label="instance_id" name="instance_id" className="col-span-6" />
              <InputArea
                label="Regex para ignorar mensagem Ex.: não é nosso|não é nosso"
                name="match_ignore_regex"
                className="col-span-12"
                rows={4}
              />
              <InputArea
                label="Regex gatilho de alerta Ex.: nosso com|nosso\. com|nosso, com|nosso recuperado|nosso, roubado|nosso\. roubado"
                name="match_positive_regex"
                className="col-span-12"
                rows={4}
              />
              <div className="col-span-full flex justify-between gap-2">
                <DialogRegexCheck />
                <div className="mx-auto" />
                <Button type="submit" isLoading={methods.formState.isSubmitting}>
                  Salvar
                </Button>
              </div>
              <p className="col-span-full text-sm">
                Observações: na regex o "."(ponto) deve ser escapado com uma contra barra "\."
              </p>
              <p className="col-span-full text-sm">
                Lógica envolvida será aplicada a 1ª regex para ignorar mensagem, e caso a mensagem
                não seja ignorada, será apicada a 2ª regex gatilho de alerta para validar se a
                mensagem possui um texto positivo.
              </p>
            </form>
          </FormProvider>
        </div>
      </div>
    </Layout.Root>
  );
}
