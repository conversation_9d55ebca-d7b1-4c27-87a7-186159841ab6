import * as Layout from "@/components/layout";
import { useHttp } from "@/hooks";
import { Button } from "@/components/ui/button";
import { toast } from "react-toastify";
import { useState } from "react";
import { TrashConfirmButton } from "@/components/trash-confirm-button";

export function Database() {
  const { httpDelete } = useHttp();
  const [isLoading, setIsLoading] = useState(false);

  async function handleResetDatabase() {
    setIsLoading(true);
    const { status } = await httpDelete("chats/settings/reset-database");
    setIsLoading(false);
    if (status === 200) {
      toast.success("Reset feito com sucesso!");
      return;
    }
    toast.error("Erro ao tentar fazer reset do banco de dados");
  }

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Settings", href: "#" },
          { name: "Reset data base", href: "#" },
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0 max-w-[600px]">
        <div>
          <Button variant="outline" disabled={isLoading}>
            <TrashConfirmButton
              onAction={handleResetDatabase}
              alterText="Confirma excluir todas mensagens e contatos da base de dados?"
            >
              Remover mensagens e contatos da base de dados
            </TrashConfirmButton>
          </Button>
        </div>
      </div>
    </Layout.Root>
  );
}
