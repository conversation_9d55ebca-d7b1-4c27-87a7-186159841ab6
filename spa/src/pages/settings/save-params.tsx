import * as Layout from "@/components/layout";
import { useHttp } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { useCallback, useEffect } from "react";
import { Input, InputArea } from "@/components/form";
import { Button } from "@/components/button";
import { toast } from "sonner";

const formSchema = z.object({
  id: z.string(),
  client_token: z.string().nonempty("campo obrigatório"),
  instance_id: z.string().nonempty("campo obrigatório"),
  instance_token: z.string().nonempty("campo obrigatório"),
  match_positive_regex: z.string().nonempty("campo obrigatório"),
});

type FormSchema = z.infer<typeof formSchema>;

export function SaveParams() {
  const methods = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
  });

  const { httpGet, httpPut } = useHttp();

  const getUser = useCallback(async () => {
    const { data } = await httpGet("/chats/settings/params");
    methods.reset(data);
  }, [httpGet, methods]);

  useEffect(() => {
    getUser();
  }, [getUser]);

  async function onSubmit(payload: FormSchema) {
    const { status } = await httpPut(`/chats/settings/params`, payload);
    if (status === 200) {
      toast.success("Parâmetros atualizado com sucesso!");
      return;
    }

    toast.error("Erro ao atualizar o parâmetros");
    return;
  }

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Settings", href: "#" },
          { name: "Parâmetros", href: "#" },
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0 max-w-[800px]">
        <div>
          <FormProvider {...methods}>
            <form
              onSubmit={methods.handleSubmit(onSubmit)}
              className="grid h-fit w-full grid-cols-12 items-start gap-4"
            >
              <Input label="client_token" name="client_token" className="col-span-12" />
              <Input label="instance_id" name="instance_id" className="col-span-12" />
              <Input label="instance_token" name="instance_token" className="col-span-12" />
              <InputArea
                label="Regex gatilho de alerta Ex.: nosso com|nosso\. com|nosso, com|nosso recuperado|nosso, roubado|nosso\. roubado"
                name="match_positive_regex"
                className="col-span-12"
                rows={4}
              />
              <div className="col-span-full flex justify-between gap-2">
                <p>Obs.: na regex o "."(ponto) deve ser escapado com uma contra barra "\."</p>
                <Button type="submit" isLoading={methods.formState.isSubmitting}>
                  Salvar
                </Button>
              </div>
            </form>
          </FormProvider>
        </div>
      </div>
    </Layout.Root>
  );
}
