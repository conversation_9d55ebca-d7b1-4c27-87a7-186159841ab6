import { PhoneOff } from "lucide-react";
import { ButtonLoading } from "./button-loading";
import { useInstance } from "./useInstance";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export function CallRejectInstance() {
  const { callRejectAuto, isLoading, instance } = useInstance();
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger className="w-full md:w-auto">
          <ButtonLoading
            disabled={instance?.me?.callRejectAuto}
            icon={PhoneOff}
            loading={isLoading}
            onClick={() => callRejectAuto()}
          >
            rejeitar chamadas
          </ButtonLoading>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          {instance?.me?.callRejectAuto ? (
            <p>Você já rejeitou as chamadas</p>
          ) : (
            <p>Click aqui para rejeitar as chamadas</p>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
