import { Loader2, type LucideIcon } from "lucide-react";
import { Button, buttonVariants } from "@/components/ui/button";
import { type VariantProps } from "class-variance-authority";

interface Props
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  icon: LucideIcon;
  loading: boolean;
  children: React.ReactNode;
}

export function ButtonLoading({ icon: Icon, loading, children, ...props }: Props) {
  return (
    <Button disabled={loading} className="w-full md:w-auto" {...props}>
      {loading && (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Aguarde ...
        </>
      )}

      {!loading && (
        <>
          <Icon className="mr-2 h-4 w-4" /> {children}
        </>
      )}
    </Button>
  );
}
