import { useHttp } from "@/hooks";
import { useCallback, useEffect, useState } from "react";
import { useStore } from "./useStore";
import { InstanceStatus } from "@/types";

export function useInstance() {
  const { httpGet, httpPut } = useHttp();
  const [isLoading, setIsLoading] = useState(false);
  const [qrCode, setQrCode] = useState<string | null>(null);
  const instance = useStore((s) => s.instance);
  const setInstance = useStore((s) => s.setInstance);

  const getStatus = useCallback(async () => {
    const { data } = await httpGet<InstanceStatus>("/chats/settings/status");
    setInstance(data);
  }, [httpGet, setInstance]);

  async function handleGenerateQrCode() {
    setIsLoading(true);
    const { data } = await httpGet("/chats/settings/connect-instance");
    setQrCode(data);
    setIsLoading(false);
  }

  async function disconnectInstance() {
    setIsLoading(true);
    await httpGet("/chats/settings/disconnect-instance");
    await getStatus();
    setIsLoading(false);
  }

  async function restartInstance() {
    setIsLoading(true);
    await httpGet("/chats/settings/restart-instance");
    setIsLoading(false);
  }

  async function callRejectAuto() {
    setIsLoading(true);
    await httpPut("/chats/settings/call-reject-auto", {});
    await getStatus();
    setIsLoading(false);
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      getStatus();
      setQrCode(null);
    }, 1000 * 15);
    return () => clearTimeout(timer);
  }, [getStatus, qrCode]);

  return {
    getStatus,
    handleGenerateQrCode,
    restartInstance,
    disconnectInstance,
    callRejectAuto,
    qrCode,
    isLoading,
    instance,
  };
}
