import * as Layout from "@/components/layout";
import { useEffect } from "react";
import { CardInstance } from "./card-instance";
import { useInstance } from "./useInstance";

export function Instance() {
  const { getStatus } = useInstance();

  useEffect(() => {
    getStatus();
  }, [getStatus]);

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Instância", href: "/settings/instance" },
        ]}
      />
      <div className="grid grid-cols-12 gap-4 p-4 pt-0">
        <CardInstance />
      </div>
    </Layout.Root>
  );
}
