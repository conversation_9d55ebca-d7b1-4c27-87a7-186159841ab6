import {
  <PERSON>,
  CardContent,
  CardD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

import { Wl } from "@/components/wrapper-label";
import { CopyToClipboard } from "@/components/copy-to-clipboard";
import { RestartInstance } from "./restart-instance";
import { DisconnectInstance } from "./disconnect-instance";
import { ConnectInstance } from "./connect-instance";
import { useInstance } from "./useInstance";
import { CallRejectInstance } from "./call-reject-instance";
import { Skeleton } from "@/components/ui/skeleton"

export function CardInstance() {
  const { instance } = useInstance();

  return (
    <Card className="col-span-12 md:col-span-6 max-w-[448px] relative">
      <CardHeader>
        <CardTitle className="flex items-center justify-between gap-6">
          {instance && (<div className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <Avatar className="h-12 w-12">
                <AvatarImage src={instance?.device?.imgUrl} alt="cover" />
                <AvatarFallback>Img</AvatarFallback>
              </Avatar>
              <div className="flex flex-col gap-1">
                {instance?.device?.phone}
                {instance?.status?.connected ? (
                  <Badge variant="default" className="justify-center">
                    Conectado
                  </Badge>
                ) : (
                  <Badge variant="destructive" className="justify-center">
                    Desconectado
                  </Badge>
                )}
              </div>
            </div>
            <CardDescription>{instance?.status?.error}</CardDescription>
          </div>)}
          {!instance && (<div className="flex flex-col gap-2">
            <div className="flex gap-2 w-[200px]">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="flex flex-col gap-1">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            </div>
            <Skeleton className="h-4 w-full" />
          </div>)}

          <ConnectInstance isConnected={instance?.status?.connected} />
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <Wl label="ID">
          <CopyToClipboard>{instance?.me?.id}</CopyToClipboard>
        </Wl>
        <Wl label="Token">
          <CopyToClipboard>{instance?.me?.token}</CopyToClipboard>
        </Wl>
      </CardContent>
      <CardFooter className="flex flex-col flex-wrap md:flex-row gap-2">
        <RestartInstance /> <DisconnectInstance /> <CallRejectInstance />
      </CardFooter>
    </Card>
  );
}
