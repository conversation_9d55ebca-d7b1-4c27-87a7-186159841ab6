import { QrCode } from "lucide-react";
import { <PERSON><PERSON><PERSON>, Toolt<PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useInstance } from "./useInstance";

interface Props {
  isConnected?: boolean;
}

export function ConnectInstance({ isConnected }: Props) {
  const { qrCode, handleGenerateQrCode } = useInstance();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger
          data-connected={isConnected}
          className="data-[connected=true]:cursor-not-allowed"
        >
          <div className="flex flex-col">
            {qrCode && <img src={qrCode!} alt="qr code" className="absolute top-2 right-2" />}
            {!qrCode && (
              <QrCode onClick={() => handleGenerateQrCode()} className="h-[84px] w-[84px]" />
            )}
            <small>gerar QR code</small>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          {isConnected && <p>Você precisa desconectar a instância para gerar um novo Qr code</p>}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
