import { Options, Permission } from "@/types";
import { create } from "zustand";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useHttp } from "@/hooks";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

// STATE
type RoleStore = {
  openForm: boolean;
  permission: Permission;
  roles: Options;
  setOpenForm: (value: boolean) => void;
  setRoles: (value: Options) => void;
  setPermission: (value: Permission) => void;
};

const useStore = create<RoleStore>((set) => ({
  openForm: false,
  roles: [],
  permission: {} as Permission,
  setOpenForm: (value) => set({ openForm: value }),
  setRoles: (value) => set({ roles: value }),
  setPermission: (value) => set({ permission: value }),
}));

// FORMS
const formSchema = z.object({
  id: z.string().optional(),
  name: z.string().nonempty("nome é obrigatório"),
  slug: z.string().nonempty("slug é obrigatório"),
  description: z.string().optional(),
  roles: z
    .object(
      { label: z.string(), value: z.string() },
      { required_error: "selecione uma opção", invalid_type_error: "selecione uma opção válida" }
    )
    .array()
    .min(1, { message: "selecione um item" }),
  status: z.boolean(),
  helper_crud: z.boolean().optional(),
});

type FormSchema = z.infer<typeof formSchema>;

const resetForm = {
  name: "",
  slug: "",
  description: "",
  roles: [],
  status: true,
  helper_crud: false,
};

export function usePermission() {
  const { httpPost, httpPut, httpDelete } = useHttp();
  const queryClient = useQueryClient();
  const { openForm, setOpenForm, roles, setRoles, permission, setPermission } = useStore();
  const methodsForm = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: resetForm,
  });

  useEffect(() => {
    methodsForm.reset({
      ...permission,
      roles: permission?.roles?.map((item) => ({ label: item.name, value: item.id! })),
      description: permission.description || "",
    });
  }, [methodsForm, permission]);

  async function onSubmit(payload: FormSchema) {
    const { id, ...data } = payload;
    if (id) {
      const { status } = await httpPut(`/auth/permissions/${id}`, {
        ...data,
        roles: data.roles.map((item) => item.value),
      });
      if (status === 200) {
        toast.success("Permissão atualizado com sucesso!");
        setOpenForm(false);
      }
    }
    if (!id) {
      const { status } = await httpPost("/auth/permissions", {
        ...data,
        roles: data.roles.map((item) => item.value),
      });
      if (status === 201) {
        toast.success("Permissão criado com sucesso!");
        setOpenForm(false);
      }
    }

    queryClient.invalidateQueries({ queryKey: ["permissions"] });
  }

  async function onDelete(id: string) {
    const { status } = await httpDelete(`/auth/permissions/${id}`);
    if (status === 200) {
      toast.success("Permissão deletado com sucesso!");
      setOpenForm(false);
    }
    queryClient.invalidateQueries({ queryKey: ["permissions"] });
  }

  return {
    openForm,
    setOpenForm,
    roles,
    setRoles,
    permission,
    setPermission,
    methodsForm,
    onSubmit,
    onDelete,
    resetForm,
  };
}
