import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input, InputHidden, Select } from "@/components/form";
import * as Check from "@/components/form/input-checkbox";
import { FormProvider } from "react-hook-form";

import { Plus } from "lucide-react";
import { usePermission } from "./usePermission";
import { Button } from "@/components/button";
import { TrashConfirmButton } from "@/components/trash-confirm-button";

export default function DialogForm() {
  const {
    methodsForm,
    onSubmit,
    onDelete,
    openForm,
    setOpenForm,
    setPermission,
    resetForm,
    roles,
  } = usePermission();
  const id = methodsForm.watch("id");

  function handleOpen() {
    setPermission(resetForm);
    setOpenForm(true);
  }

  return (
    <div className="col-span-12 md:col-span-4 flex items-center justify-center">
      <Dialog open={openForm} onOpenChange={(value) => setOpenForm(value)}>
        <DialogTrigger className="w-full h-full" onClick={handleOpen} asChild>
          <Button variant="outline" className="h-9">
            <Plus className="w-4 h-4" /> Nova permissão
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{id ? "Editar" : "Criar"} Permissão</DialogTitle>
            <DialogDescription>
              uma nova permissão permite associada a um Cargo permite acessar um recurso no sistema.
            </DialogDescription>
          </DialogHeader>
          <FormProvider {...methodsForm}>
            <form
              onSubmit={methodsForm.handleSubmit(onSubmit)}
              className="grid h-fit w-full grid-cols-12 items-start gap-4"
            >
              <InputHidden name="id" />
              <Input label="Nome" name="name" className="col-span-12" />
              <Input label="Slug" name="slug" className="col-span-12" />
              <Select label="Cargos" name="roles" isMulti options={roles} />
              <Input label="Descrição" name="description" className="col-span-12" />
              <div className="col-span-3 flex items-center pt-1">
                <Check.Control name="status" label="Status" />
              </div>
              {!id && (
                <div className="col-span-9 flex items-center justify-end pt-1">
                  <Check.Control name="helper_crud" label="criar demais permissões CRUD?" />
                </div>
              )}

              <div className="col-span-full flex justify-end mt-4">
                {id && (
                  <TrashConfirmButton onAction={() => onDelete(id)}> Remover</TrashConfirmButton>
                )}
                <div className="mx-auto" />
                <Button type="submit" isLoading={methodsForm.formState.isSubmitting}>
                  Salvar
                </Button>
              </div>
            </form>
          </FormProvider>
        </DialogContent>
      </Dialog>
    </div>
  );
}
