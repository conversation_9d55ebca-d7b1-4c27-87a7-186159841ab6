import * as Layout from "@/components/layout";
import { useQuery } from "@tanstack/react-query";
import { Role } from "@/types";
import { useHttp } from "@/hooks";
import { DataTable } from "./data-table/data-table";
import { columns } from "./data-table/columns";
import { usePermission } from "./usePermission";

export function Permissions() {
  const { httpGet } = useHttp();
  const { setOpenForm, setPermission, setRoles } = usePermission();

  const { data, isLoading } = useQuery<Role[]>({
    queryKey: ["permissions"],
    queryFn: async () => {
      const { status, data } = await httpGet("/auth/permissions");
      return status === 200 ? data : [];
    },
  });

  useQuery({
    queryKey: ["roles"],
    queryFn: async () => {
      const { status, data } = await httpGet<Role[]>("/auth/roles");
      if (status === 200) {
        setRoles(
          data.map((group) => ({
            label: group.name,
            value: group.id!,
          }))
        );
      }
      return data;
    },
  });

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Settings", href: "#" },
          { name: "Cargos", href: "/settings/roles" },
          { name: "Permissões", href: "#" },
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div>
          <h1>Permissões</h1>

          <DataTable
            columns={columns}
            data={data || []}
            isLoading={isLoading}
            handleRowClick={(row) => {
              setPermission(row.original);
              setOpenForm(true);
            }}
          />
        </div>
      </div>
    </Layout.Root>
  );
}
