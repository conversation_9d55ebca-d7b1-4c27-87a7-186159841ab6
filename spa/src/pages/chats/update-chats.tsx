import { useCallback, useState } from "react";
import * as Layout from "@/components/layout";
import { useHttp } from "@/hooks";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

export function UpdateChats() {
  const [isLoading, setIsLoading] = useState(false);

  const { httpGet } = useHttp();

  const updateChats = useCallback(async () => {
    setIsLoading(true);
    const { status, data } = await httpGet("/chats/update");
    if (status === 200) {
      toast.success(`${data.length} contatos encontrados!`, {
        description: "os contatos foram atualizados com sucesso!",
      });
    }
    setIsLoading(false);
  }, [httpGet]);

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Lista de contatos", href: "/chats" },
          { name: "Atualizar contatos" },
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0 w-[400px]">
        <div className="flex flex-col gap-2">
          <h1>Atualizar contatos</h1>

          <Button size="lg" onClick={updateChats} disabled={isLoading} className="w-full">
            {isLoading && <Loader2 className="animate-spin" />}
            Click aqui para atualizar os contatos
          </Button>
        </div>
      </div>
    </Layout.Root>
  );
}
