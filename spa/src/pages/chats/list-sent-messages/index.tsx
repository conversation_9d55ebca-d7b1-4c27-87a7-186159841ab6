import * as Layout from "@/components/layout";
import { DataTableAsync } from "@/components/data-table-async";
import { columns } from "./columns";

export function ListSentMessages() {
  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Mensagens enviadas", href: "/chats/list-sent-messages" },
        ]}
      />
      <Layout.Content className="py-0">
        <DataTableAsync
          url="/chats/messages/sent"
          columns={columns}
          placeholder="Filtrar por: Phone ou Mensagem"
          handleRowClick={() => {}}
        />
      </Layout.Content>
    </Layout.Root>
  );
}
