import { IncomingMessage } from "@/types";
import { ColumnDef } from "@tanstack/react-table";
import { CopyToClipboard } from "@/components/copy-to-clipboard";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { format } from "date-fns";

export const columns: ColumnDef<IncomingMessage>[] = [
  {
    accessorKey: "phone",
    header: "Phone",
    size: 200,
    cell: ({ row }) => {
      return (
        <div className="flex max-w-[240px] gap-2 truncate">
          <CopyToClipboard text={row.original.phone}>
            <p className="font-mono"> {row.original.phone} </p>
          </CopyToClipboard>
        </div>
      );
    },
  },
  {
    accessorKey: "chat.name",
    header: "Usuário / Cliente",
    size: 200,
    cell: ({ row }) => {
      return (
        <div className="flex max-w-[260px] gap-2 truncate">
          <span> {row.original?.chat?.name || "N/A"} </span>
        </div>
      );
    },
  },
  {
    accessorKey: "message",
    header: "Mensagem",
    cell: ({ row }) => {
      return (
        <div className="flex items-center max-w-[400px] truncate">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger> {row.original.message}</TooltipTrigger>
              <TooltipContent className="text-center max-w-[380px]">
                <p>{row.original.message}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      );
    },
  },
  {
    accessorKey: "momment",
    header: "Data",
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          {row?.original?.momment ? format(row.original.momment, "dd/MM/yyyy HH:mm") : "N/A"}
        </div>
      );
    },
  },
];
