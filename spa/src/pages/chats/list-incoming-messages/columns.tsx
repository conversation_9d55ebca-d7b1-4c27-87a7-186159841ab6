import { IncomingMessage } from "@/types";
import { ColumnDef } from "@tanstack/react-table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { format } from "date-fns";

export const columns: ColumnDef<IncomingMessage>[] = [
  {
    accessorKey: "chat.name",
    header: "Usuário / Cliente",
    size: 200,
    cell: ({ row }) => {
      return <div className="flex items-center max-w-[220px]">{row.original?.chat?.name}</div>;
    },
  },
  {
    accessorKey: "message",
    header: "Mensagem",
    cell: ({ row }) => {
      return (
        <div className="flex items-center max-w-[700px] truncate">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger> {row.original.message}</TooltipTrigger>
              <TooltipContent className="text-center max-w-[380px]">
                <p>{row.original.message}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      );
    },
  },
  {
    accessorKey: "momment",
    header: "Data",
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          {row?.original?.momment ? format(row.original.momment, "dd/MM/yyyy HH:mm") : "N/A"}
        </div>
      );
    },
  },
];
