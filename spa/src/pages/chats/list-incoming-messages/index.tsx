import * as Layout from "@/components/layout";
import { columns } from "./columns";
import { DataTableAsync } from "@/components/data-table-async";

export function ListIncomingMessages() {
  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Mensa<PERSON> recebidas", href: "/chats/list-incoming-messages" },
        ]}
      />
      <Layout.Content className="py-0">
        <DataTableAsync
          url="/chats/messages/incoming"
          columns={columns}
          placeholder="Filtrar por: Phone ou Mensagem"
          handleRowClick={() => {}}
        />
      </Layout.Content>
    </Layout.Root>
  );
}
