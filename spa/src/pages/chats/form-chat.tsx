import * as Layout from "@/components/layout";
import { useHttp } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { z } from "zod";
import { useCallback, useEffect } from "react";
import { Input } from "@/components/form";
import { Button } from "@/components/button";
import { toast } from "sonner";
import { TrashConfirmButton } from "@/components/trash-confirm-button";

const formSchema = z.object({
  name: z.string().nonempty("Nome é obrigatório"),
  phone: z.string().nonempty("Número é obrigatório"),
  // type: z.string().nonempty("Tipo é obrigatório"),
  // type: z.object(
  //   { label: z.string(), value: z.string() },
  //   { required_error: "selecione uma opção", invalid_type_error: "selecione uma opção válida" }
  // ),
  //TODO update schema to include status optional
  status: z.boolean(),
});

type FormSchema = z.infer<typeof formSchema>;

export function FormChat() {
  const { id } = useParams<{ id: string }>();
  const nav = useNavigate();
  const methods = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      phone: "",
      // type: { label: "", value: "" },
      status: true,
    },
  });

  const { httpGet, httpPut, httpPost, httpDelete } = useHttp();

  const getChat = useCallback(
    async (id: string) => {
      if (!id) return;
      const { data } = await httpGet(`/chats/${id}`);
      methods.reset({
        ...data,
        // type: {
        //   label: data.type === "insurer" ? "Seguradora" : "Usuário",
        //   value: data.type === "insurer" ? "insurer" : "user",
        // },
      });
    },
    [httpGet, methods]
  );

  useEffect(() => {
    if (!id) return;
    getChat(id);
  }, [getChat, id]);

  async function onSubmit(payload: FormSchema) {
    if (id) {
      const { status } = await httpPut(`/chats/${id}`, {
        ...payload,
        type: "unknown",
      });
      if (status === 200) {
        toast.success("Contato atualizado com sucesso!");
        return;
      }

      toast.error("Erro ao atualizar o contato");
      return;
    }

    const { status, data } = await httpPost<{ id: string }>(`/chats`, {
      ...payload,
      type: "unknown",
    });
    if (status === 201) {
      nav(`/chat/${data.id}`, { replace: true });
      toast.success("Contato criado com sucesso!");
      return;
    }

    toast.error("Erro ao criar o chat");
  }

  async function onDelete() {
    if (!id) return;
    const { status } = await httpDelete(`/chats/${id}`);
    if (status === 200) {
      toast.success("Contato deletado com sucesso!");
      nav("/chats", { replace: true });
      return;
    }
  }

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Listar de Contatos", href: "/chats" },
          { name: id ? "Editar Contato" : "Novo Contato", href: "#" },
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0 max-w-[600px]">
        <div>
          <FormProvider {...methods}>
            <form
              onSubmit={methods.handleSubmit(onSubmit)}
              className="grid h-fit w-full grid-cols-12 items-start gap-4"
            >
              <Input label="Nome" name="name" className="col-span-12" />
              <Input
                label="Número (5521xxxxxxxxx ou 5521xxxxxxxxx-group)"
                name="phone"
                className="col-span-12"
              />
              {/* <Select
                label="Tipo (Usuário faz pedido Seguradora responde pedido)"
                name="type"
                className="col-span-12"
                options={[
                  { value: "user", label: "Usuário" },
                  { value: "insurer", label: "Seguradora" },
                ]}
              />
              <div className="col-span-12 flex items-center pt-1">
                <Check.Control
                  name="status"
                  label={status ? "Ativo (click para desativar)" : "Inativo (click para ativar)"}
                />
              </div> */}
              <div className="col-span-full flex justify-end">
                {id && (
                  <TrashConfirmButton onAction={onDelete}> Remover contato</TrashConfirmButton>
                )}
                <div className="mx-auto" />
                <Button type="submit" isLoading={methods.formState.isSubmitting}>
                  Salvar
                </Button>
              </div>
            </form>
          </FormProvider>
        </div>
      </div>
    </Layout.Root>
  );
}
