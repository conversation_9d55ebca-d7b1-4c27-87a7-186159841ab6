import { Chat } from "@/types";
import { ColumnDef } from "@tanstack/react-table";
import { CopyToClipboard } from "@/components/copy-to-clipboard";
import { Button } from "@/components/ui/button";
import { ArrowUpDown } from "lucide-react";

export const columns: ColumnDef<Chat>[] = [
  {
    accessorKey: "name",
    header: "Nome",
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          <CopyToClipboard>{row.original.name}</CopyToClipboard>
        </div>
      );
    },
  },
  {
    accessorKey: "phone",
    header: "Número",
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          <CopyToClipboard>{row.original.phone}</CopyToClipboard>
        </div>
      );
    },
  },
  {
    accessorKey: "groups",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Grupos
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="flex items-center">{row.original?.groups}</div>;
    },
  },
  // {
  //   accessorKey: "type",
  //   header: ({ column }) => {
  //     return (
  //       <Button
  //         variant="ghost"
  //         className="p-0"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //       >
  //         Tipo
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  //   cell: ({ row }) => {
  //     return (
  //       <div className="flex items-center">
  //         {row.original.type === "user" && "Usuário"}
  //         {row.original.type === "insurer" && "Seguradora"}
  //       </div>
  //     );
  //   },
  // },
  // {
  //   accessorKey: "status",
  //   header: ({ column }) => {
  //     return (
  //       <Button
  //         variant="ghost"
  //         className="p-0"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //       >
  //         Status
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  //   cell: ({ row }) => {
  //     return (
  //       <div className="flex items-center">
  //         {row.original.status ? (
  //           <Badge variant="outline">Ativo</Badge>
  //         ) : (
  //           <Badge variant="destructive">Inativo</Badge>
  //         )}
  //       </div>
  //     );
  //   },
  // },
];
