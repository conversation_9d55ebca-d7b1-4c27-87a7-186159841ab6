import { useCallback, useEffect, useState } from "react";
import * as Layout from "@/components/layout";
import { useHttp } from "@/hooks";
import { DataTable } from "./data-table/data-table";
import { columns } from "./data-table/columns";
import { useNavigate } from "react-router-dom";

export function ListChats() {
  const [chats, setChats] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const nav = useNavigate();

  const { httpGet } = useHttp();

  const getChats = useCallback(async () => {
    setIsLoading(true);
    const { data } = await httpGet("/chats");
    setChats(data);
    setIsLoading(false);
  }, [httpGet]);

  const updateChat = useCallback(async () => {
    setIsLoading(true);
    const { data } = await httpGet("/chats/update");
    setChats(data);
    setIsLoading(false);
  }, [httpGet]);

  useEffect(() => {
    getChats();
  }, [getChats]);

  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Listar de Grupo de Usuários", href: "/groups-users" },
          { name: "Lista de Contatos", href: "/chats" },
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="flex flex-col gap-2">
          <h1>Lista de Contatos</h1>
          <DataTable
            columns={columns}
            data={chats}
            isLoading={isLoading}
            handleRowClick={(row) => nav(`/chat/${row.original.id}`)}
            updateChat={updateChat}
          />
        </div>
      </div>
    </Layout.Root>
  );
}
