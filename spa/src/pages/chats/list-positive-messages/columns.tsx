import { PositiveMessage } from "@/types";
import { ColumnDef } from "@tanstack/react-table";
import { CopyToClipboard } from "@/components/copy-to-clipboard";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { format } from "date-fns";

export const columns: ColumnDef<PositiveMessage>[] = [
  {
    accessorKey: "requested_message.chat.name",
    header: "Usuário",
    cell: ({ row }) => {
      return (
        <div className="flex items-center max-w-[100px] truncate">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>{row.original.requested_message?.chat?.name}</TooltipTrigger>
              <TooltipContent className="text-center max-w-[380px]">
                <p>{row.original.requested_message?.chat?.name}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      );
    },
  },
  {
    accessorKey: "forwarded_message.message",
    header: "Consulta",
    cell: ({ row }) => {
      return (
        <div className="flex items-center max-w-[200px] truncate">
          <CopyToClipboard>{row.original?.forwarded_message?.message}</CopyToClipboard>
        </div>
      );
    },
  },
  {
    accessorKey: "replied_message.chat.name",
    header: "Cliente",
    cell: ({ row }) => {
      return (
        <div className="flex items-center max-w-[180px] truncate">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>{row.original?.replied_message?.chat?.name}</TooltipTrigger>
              <TooltipContent className="text-center max-w-[380px]">
                <p>{row.original?.replied_message?.chat?.name}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      );
    },
  },
  {
    accessorKey: "replied_message.message",
    header: "Mensagem Positivada",
    cell: ({ row }) => {
      return (
        <div className="flex items-center max-w-[420px] truncate">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>{row.original?.replied_message?.message}</TooltipTrigger>
              <TooltipContent className="text-center max-w-[380px]">
                <p>{row.original?.replied_message?.message}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      );
    },
  },

  {
    accessorKey: "created_at",
    header: "Data",
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          {row?.original?.created_at ? format(row.original?.created_at, "dd/MM/yyyy HH:mm") : "N/A"}
        </div>
      );
    },
  },
];
