import * as Layout from "@/components/layout";
import { DataTableAsync } from "@/components/data-table-async";
import { columns } from "./columns";

export function ListPositiveMessages() {
  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Mensagens positivas", href: "/chats/list-positive-messages" },
        ]}
      />
      <Layout.Content className="py-0">
        <DataTableAsync
          url="/chats/messages/positive-responses"
          columns={columns}
          placeholder="Filtrar por: Consulta e Mensagem"
          handleRowClick={() => {}}
        />
      </Layout.Content>
    </Layout.Root>
  );
}
