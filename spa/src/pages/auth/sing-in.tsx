import { Input } from "@/components/form";
import * as InputCheckbox from "@/components/form/input-checkbox";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@/components/button";
import { Link, useNavigate } from "react-router-dom";
import { SignInIcon } from "@/styles";
import { toast } from "sonner";

import * as Layout from "./components/layout";

import { useStoreAuth } from "@/states/useStoreAuth";

const FormSchema = z.object({
  user: z.string().email({ message: "e-mail é obrigatório" }),
  password: z.string().min(1, { message: "senha é obrigatório" }).trim(),
  remember: z.boolean().default(false).optional(),
});

type FormSchema = z.infer<typeof FormSchema>;

const defaultValues = {
  user: import.meta.env.DEV ? "<EMAIL>" : localStorage.getItem("remember") || "",
  password: import.meta.env.DEV ? import.meta.env.VITE_PASSWORD_DEV_USER : "",
  remember: localStorage.getItem("remember") ? true : false,
};

export function SingIn() {
  const nav = useNavigate();
  const { loading, signIn } = useStoreAuth();

  const methods = useForm<FormSchema>({
    resolver: zodResolver(FormSchema),
    defaultValues,
  });

  async function onSubmit(payload: FormSchema) {
    if (payload.remember) {
      localStorage.setItem("remember", payload.user);
    } else {
      localStorage.setItem("remember", "");
    }

    const isLogin = await signIn(payload.user, payload.password);

    if (isLogin) {
      nav("/home");
      return;
    }

    methods.setValue("password", "");

    toast.error("Erro ao acessar o sistema", {
      description: "Senha ou usuário inválido",
    });
  }

  return (
    <Layout.Root>
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Entrar no sistema</h1>
        </div>
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)} className="flex flex-col gap-2">
            <Input label="E-mail" name="user" />
            <Input label="Senha" name="password" type="password" />
            <div className="flex items-center justify-between">
              <InputCheckbox.Control name="remember" label="lembrar" />
              <Link to="/auth/forgot-password" className="text-sm text-primary hover:underline">
                esqueci a senha
              </Link>
            </div>
            <Button type="submit" className="mt-2 gap-2" isLoading={loading}>
              <SignInIcon /> Acessar o sistema
            </Button>
          </form>
        </FormProvider>
        <p className="px-8 text-center text-sm text-muted-foreground">
          Ao clicar em acessar o sistema, você concorda com nossos{" "}
          <a href="/terms" className="underline underline-offset-4 hover:text-primary">
            Termos de Serviços
          </a>
          {" e "}
          <a href="/privacy" className="underline underline-offset-4 hover:text-primary">
            Política de Privacidade
          </a>
          .
        </p>
      </div>
    </Layout.Root>
  );
}
