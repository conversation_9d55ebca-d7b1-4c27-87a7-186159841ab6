import { Input } from "@/components/form";
import { Form<PERSON><PERSON>ider, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/button";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useHttp } from "@/hooks";
import { toast } from "@/utils";
import * as Layout from "./components/layout";

const strongPasswordRegex =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,}$/;

const formSchema = z
  .object({
    password: z
      .string()
      .regex(
        strongPasswordRegex,
        "Senha fraca! Combine letras maiúsculas, minúsculas, números e símbolos"
      )
      .min(8, { message: "senha é obrigatória, 8 carácteres mínimos" }),
    password_confirm: z.string(),
  })
  .refine((data) => data.password === data.password_confirm, {
    message: "as senhas não conferem",
    path: ["password_confirm"],
  });

type FormSchema = z.infer<typeof formSchema>;

const defaultValues = {
  password: "",
};

export function ResetPassword() {
  const { token } = useParams<{ token: string }>();
  const { httpPost } = useHttp();

  const nav = useNavigate();

  const methods = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  async function onSubmit(payload: FormSchema) {
    const { status } = await httpPost("/auth/reset", {
      ...payload,
      token,
    });

    if (status === 200) {
      toast.success("Senha alterada com sucesso!");
      nav("/login");
      return;
    }

    methods.reset();
  }

  return (
    <Layout.Root>
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Criar nova senha</h1>
        </div>
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)} className="flex flex-col gap-2">
            <Input label="Nova senha" name="password" type="password" />
            <Input label="Confirme a nova senha" name="password_confirm" type="password" />
            <div className="flex justify-end items-center">
              <Link to="/login" className="text-sm text-primary hover:underline">
                voltar para tela de login
              </Link>
            </div>
            <Button type="submit" className="mt-2 gap-2" isLoading={methods.formState.isSubmitting}>
              Criar uma nova Senha
            </Button>
          </form>
        </FormProvider>
        <p className="px-8 text-center text-sm text-muted-foreground">
          Ao clicar em continuar, você concorda com nossos{" "}
          <a href="/terms" className="underline underline-offset-4 hover:text-primary">
            Termos de Serviços
          </a>
          {" e "}
          <a href="/privacy" className="underline underline-offset-4 hover:text-primary">
            Política de Privacidade
          </a>
          .
        </p>
      </div>
    </Layout.Root>
  );
}
