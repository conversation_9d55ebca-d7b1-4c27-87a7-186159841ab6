import { ComponentProps, ReactNode } from "react";
import { twMerge } from "tailwind-merge";

import loginImg from "@/assets/login.png";

interface Props extends ComponentProps<"div"> {
  children: ReactNode;
}

export function Root({ children, className, ...rest }: Props) {
  return (
    <div className={twMerge("bg-background", className)} {...rest}>
      <div className="flex items-center justify-center md:hidden">
        <img src={loginImg} alt="" className="mt-4 block w-60 p-1" />
      </div>
      <div className="container relative h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0">
        <div className="relative hidden h-full flex-col bg-muted p-10 text-white dark:border-r lg:flex">
          <div className="absolute inset-0 bg-zinc-900" />
          <div className="relative z-20 my-auto flex flex-col gap-4 items-center justify-center">
            <div className="flex text-4xl tracking-tight uppercase font-thin gap-2">
              <div className="font-bold">Mirror</div>Easy
            </div>
            <img src={loginImg} alt="" className="w-[26rem]" />
          </div>
          <div className="relative z-20 mt-auto">
            <blockquote className="space-y-2">
              <footer className="text-sm text-zinc-400">bento.dev.br</footer>
            </blockquote>
          </div>
        </div>
        <div className="lg:p-8">{children}</div>
      </div>
    </div>
  );
}
