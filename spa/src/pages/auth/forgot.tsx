import { Input } from "@/components/form";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/button";
import { Link } from "react-router-dom";
import { useHttp } from "@/hooks";
import { toast } from "@/utils";
import * as Layout from "./components/layout";

const FormSchema = z.object({
  email: z.string().email({ message: "informe um e-mail válido" }),
});

type FormSchema = z.infer<typeof FormSchema>;

export function Forgot() {
  const { httpPost } = useHttp();
  const methods = useForm<FormSchema>({
    resolver: zodResolver(FormSchema),
    defaultValues: { email: "" },
  });

  async function onSubmit(payload: FormSchema) {
    const { status, data } = await httpPost("/auth/forgot", payload);
    methods.reset();
    if (status === 200) {
      toast.success(data.message);
    }
  }

  return (
    <Layout.Root>
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Recupera senha</h1>
        </div>
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)} className="flex flex-col gap-2">
            <Input label="E-mail" name="email" />
            <div className="flex justify-end">
              <Link to="/login" className="text-sm text-primary hover:underline">
                voltar para tela de login
              </Link>
            </div>
            <Button type="submit" className="mt-2">
              Recuperar acesso
            </Button>
          </form>
        </FormProvider>
        <p className="px-8 text-center text-sm text-muted-foreground">
          Informe o e-mail que você utiliza para acessar o sistema.
        </p>
      </div>
    </Layout.Root>
  );
}
