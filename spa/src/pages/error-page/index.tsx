import { useNavigate } from "react-router-dom";

export const ErrorPage = (): JSX.Element => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate("/");
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100">
      <div className="p-8 bg-white shadow-md rounded-lg text-center">
        <h1 className="text-4xl font-bold mb-4">404</h1>
        <p className="text-xl mb-4">Página não encontrada</p>
        <button onClick={handleGoHome} className="bg-blue-500 text-white rounded p-2">
          Voltar para a página inicial
        </button>
      </div>
    </div>
  );
};
