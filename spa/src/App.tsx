import { BrowserRouter } from "react-router-dom";
import { Routes } from "./routes";
import { Providers } from "./providers";
import { Toaster } from "@/components/ui/sonner";
import { ThemeProvider } from "@/components/theme-provider";

function App() {
  return (
    <BrowserRouter
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true,
      }}
    >
      <Providers>
        <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
          <Routes />
          <Toaster richColors key={"toaster"} position="top-right" />
        </ThemeProvider>
      </Providers>
    </BrowserRouter>
  );
}

export default App;
