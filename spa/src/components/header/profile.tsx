import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { useStoreAuth } from "@/states/useStoreAuth";

import { SignOutIcon, KeyIcon, ProfileIcon } from "@/styles";
import { initialName } from "@/utils";

import { Link } from "react-router-dom";

export function Profile() {
  const { logout } = useStoreAuth();

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger>
          <div className="flex items-center text-zinc-700">
            <div className="mr-2 hidden flex-col items-end md:flex">
              <span className="text-md leading-4">João</span>
              <span className="text-xs leading-3">joao@gmail/com</span>
            </div>
            <div>
              <Avatar className="h-9 w-9 md:h-11 md:w-11">
                <AvatarImage src={""} alt="avatar" />
                <AvatarFallback className="bg-zinc-200">{initialName("João Silva")}</AvatarFallback>
              </Avatar>
            </div>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="flex w-auto flex-col gap-2" side="bottom" align="end">
          <Link to="/me">
            <DropdownMenuItem>
              <ProfileIcon className="mr-2 h-4 w-4" /> Meus dados
            </DropdownMenuItem>
          </Link>
          <Link to="/change-password">
            <DropdownMenuItem>
              <KeyIcon className="mr-2 h-4 w-4" /> Alterar senha
            </DropdownMenuItem>
          </Link>
          <Link to="/logout" onClick={logout}>
            <DropdownMenuItem>
              <SignOutIcon className="mr-2 h-4 w-4" /> Sair do sistema
            </DropdownMenuItem>
          </Link>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
