import { Download, X } from "lucide-react";

import { Dialog, DialogContent } from "@/components/ui/dialog";

import { But<PERSON> } from "../ui/button";
import { usePreviewModalStore } from "./usePreviewModalStore";

export function PreviewModal() {
  const { isOpen, url, extension, error, closeModal } = usePreviewModalStore();

  return (
    <Dialog open={isOpen} onOpenChange={closeModal}>
      <DialogContent className="overflow-hidden p-0">
        {extension !== "pdf" && (
          <div className="absolute right-2 top-2 z-10" onClick={closeModal}>
            <X className="h-6 w-6 cursor-pointer text-white shadow-md" />
          </div>
        )}

        {error && (
          <div className="flex flex-col items-center gap-2 p-4">
            {error}
            <p>Não consigo fazer um preview, mas você pode baixar o arquivo</p>

            {error !== "Failed to get signed URL" && (
              <Button
                onClick={() => window.open(url, "_blank")}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Download className="text-foreground/50" size={16} />
                clicando aqui
              </Button>
            )}
          </div>
        )}
        {extension === "pdf" && (
          <iframe src={url} className="h-[calc(100vh-80px)] w-full" />
        )}
        {extension === "png" && (
          <img src={url} alt="Imagem" className="h-auto w-full" />
        )}
      </DialogContent>
    </Dialog>
  );
}
