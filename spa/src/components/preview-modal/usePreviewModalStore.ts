import { create } from "zustand";

import { getCookie } from "@/utils";

interface ModalState {
  isOpen: boolean;
  url: string;
  extension: string | null;
  openPreviewModal: (fileId: string) => Promise<void>;
  closeModal: () => void;
  error: string | null;
}

function validateExtension(url: string) {
  const regex = /\.([a-zA-Z0-9]+)(\?|$)/;
  const match = url.match(regex);

  if (match) {
    const extension = match[1].toLocaleLowerCase();
    const allowedExtensions = ["jpg", "jpeg", "png", "gif", "webp", "pdf"];
    if (!allowedExtensions.includes(extension)) {
      return null;
    }

    return extension === "pdf" ? "pdf" : "png";
  }
  return null;
}

async function getSignedUrl(fileId: string): Promise<string> {
  try {
    const token = getCookie("token");

    const response = await fetch(
      `${import.meta.env.VITE_API_URL}/files/get-signed-url/${fileId}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers":
            "Origin, X-Requested-With, Content-Type, Accept",
        },
      },
    );

    if (response.ok) {
      const data = await response.json();
      return data.signedUrl;
    }

    throw new Error("Failed to get signed URL");
  } catch (error) {
    console.error("Error fetching signed URL:", error);
    throw error; // Propaga o erro para tratamento superior
  }
}

export const usePreviewModalStore = create<ModalState>((set) => ({
  isOpen: false,
  url: "",
  extension: null,
  error: null,
  openPreviewModal: async (fileId: string) => {
    try {
      set({ isOpen: false, url: "", extension: null, error: null });

      const url = await getSignedUrl(fileId);
      const extension = validateExtension(url);
      if (!extension) {
        set({
          isOpen: true,
          url,
          extension: null,
          error: "Arquivo não suportado.",
        });
        return;
      }
      set({ isOpen: true, url, extension, error: null });
    } catch (error) {
      console.error("Error opening modal:", error);
      set({
        isOpen: true,
        url: "",
        extension: null,
        error: error instanceof Error ? error.message : "Erro desconhecido",
      });
    }
  },
  closeModal: () => set({ isOpen: false, url: "", extension: null }),
}));
