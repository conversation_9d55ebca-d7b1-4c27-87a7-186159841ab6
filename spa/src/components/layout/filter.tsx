import { useState, ComponentProps } from "react";
import { Field } from "@/components/form";
import { FormProvider, useForm } from "react-hook-form";
import { z, ZodType } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { SearchIcon, TuneIcon } from "@/styles";
import { twMerge } from "tailwind-merge";
import { Button } from "@/components/ui/button";

const formSchemaDefault = z.object({
  filter: z.string().trim(),
});

interface SearchProps extends ComponentProps<"div"> {
  formSchema?: ZodType;
  onSubmit: (payload: any) => Promise<void>;
  children?: React.ReactNode;
}

export function Filter({
  onSubmit,
  formSchema = formSchemaDefault,
  children,
  className,
}: SearchProps) {
  const [show, setShow] = useState(false);
  type FormSchema = z.infer<typeof formSchemaDefault>;

  const methods = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      filter: "",
    },
  });

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="relative flex gap-2">
        <div className="flex w-full overflow-hidden rounded-md border">
          <button type="submit">
            <SearchIcon className="ml-2" />
          </button>
          <Field
            name="filter"
            type="search"
            className="focus:shadow-outline w-full appearance-none border-0 py-2 pl-2 pr-3 leading-tight text-gray-700 focus:outline-none"
          />
          <button type="button" onClick={() => setShow(!show)}>
            <TuneIcon className="mr-2" />
          </button>

          <div
            data-active={show}
            className={twMerge(
              "absolute left-0 top-10 z-20 flex flex-col rounded-md border bg-white px-4 py-4 shadow-md transition-opacity duration-200",
              show ? "opacity-1" : "-top-[1000px] opacity-0",
              className,
            )}
          >
            {children}
            <div className="mt-4 flex justify-between">
              <Button type="reset" size="sm">
                Limpar
              </Button>
              <Button type="submit" size="sm" className="px-6">
                Filtrar
              </Button>
            </div>
          </div>
        </div>
      </form>
    </FormProvider>
  );
}
