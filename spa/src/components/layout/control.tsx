import { useLayoutState } from "@/states";
import { ComponentProps, useEffect } from "react";
import { twMerge } from "tailwind-merge";

interface ControlProps extends ComponentProps<"div"> {
  children: React.ReactNode;
}

export function Control({ children, className, ...rest }: ControlProps) {
  const [setHasControlArea] = useLayoutState((s) => [s.setHasControlArea]);

  useEffect(() => {
    setHasControlArea(true);
    return () => setHasControlArea(false);
  }, [setHasControlArea]);

  return (
    <div
      className={twMerge("flex h-16 items-center justify-between border-b px-4", className)}
      {...rest}
    >
      {children}
    </div>
  );
}
