import { ComponentProps, ReactNode } from "react";
import { twMerge } from "tailwind-merge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "../ui/skeleton";


interface Props extends ComponentProps<"div"> {
  children: ReactNode;
  isLoading?: boolean;
}
export function Content({ children, isLoading = false, className }: Props) {
  return (
    <ScrollArea
      className={twMerge(
        "h-[calc(100vh_-_4rem)] max-h-[calc(100vh-4rem)] w-full p-4",
        className,
      )}
    >
      <>
        {isLoading && (
          <div className="absolute left-0 right-0 top-0 z-50 flex h-full flex-col overflow-x-hidden">
            <Skeleton className="w-full h-1 bg-foreground rounded-none" />
            <div className="h-full w-full overflow-hidden opacity-50" />
          </div>
        )}

        {children}
      </>
    </ScrollArea>
  );
}
