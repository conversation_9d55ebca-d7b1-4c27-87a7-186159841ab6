import { Link } from "react-router-dom";

import {
  Breadcrumb as B<PERSON>crumbUI,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { ModeToggle } from "../mode-toogle";

interface BreadcrumbProps {
  links: { name: string; href?: string }[] | string;
}

export function Breadcrumb({ links }: BreadcrumbProps) {
  const isString = typeof links === "string";


  return (
    <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4 justify-between w-full">
        <SidebarTrigger className="-ml-1" />

        <Separator orientation="vertical" className="mr-2 h-4" />
        <BreadcrumbUI>
          <BreadcrumbList>
            {links && isString && <span className="hidden md:flex">{links}</span>}

            {!isString &&
              links?.map((link, index) => {
                if (link.href) {
                  return (
                    <div key={link.href} className="flex items-center">
                      <BreadcrumbItem className="hidden md:block">
                        <BreadcrumbLink asChild>
                          <Link to={link.href} replace>
                            {link.name}
                          </Link>
                        </BreadcrumbLink>
                      </BreadcrumbItem>
                      <BreadcrumbSeparator
                        className="data-[last=true]:hidden"
                        data-last={links.length === index + 1}
                        key={`separator-${index}`}
                      />
                    </div>
                  );
                }
                return (
                  <BreadcrumbItem key={link.name}>
                    <BreadcrumbPage>{link.name}</BreadcrumbPage>
                  </BreadcrumbItem>
                );
              })}
          </BreadcrumbList>
        </BreadcrumbUI>
        <div className="mx-auto" />
        <ModeToggle />
      </div>



    </header>
  );
}
