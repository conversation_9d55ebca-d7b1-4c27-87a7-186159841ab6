import { Field } from "@/components/form";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { SearchIcon } from "@/styles";
import { twMerge, ClassNameValue } from "tailwind-merge";

const formSchema = z.object({
  filter: z.string().trim(),
});

type FormSchema = z.infer<typeof formSchema>;

interface SearchProps {
  onSubmit: (payload: FormSchema) => Promise<void>;
  className?: ClassNameValue;
}

export function Search({ onSubmit, className }: SearchProps) {
  const methods = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      filter: "",
    },
  });

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className={twMerge("flex gap-2 ", className)}>
        <div className="flex border rounded-md overflow-hidden w-full">
          <button type="submit">
            <SearchIcon className="ml-2" />
          </button>
          <Field
            name="filter"
            type="filter"
            className="appearance-none border-0 w-full py-2 pl-2 pr-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          />
        </div>
      </form>
    </FormProvider>
  );
}
