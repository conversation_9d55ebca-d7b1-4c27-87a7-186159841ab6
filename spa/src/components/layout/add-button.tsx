import { PlusIcon } from "@/styles";
import { ComponentProps } from "react";
import { twMerge } from "tailwind-merge";

interface ButtonProps extends ComponentProps<"button"> {}
export function AddButton({ className, ...rest }: ButtonProps) {
  return (
    <button
      className={twMerge("bg-zinc-100 hover:bg-zinc-200 p-1.5 rounded-md", className)}
      {...rest}
    >
      <PlusIcon className="text-zinc-500 w-6 h-6" />
    </button>
  );
}
