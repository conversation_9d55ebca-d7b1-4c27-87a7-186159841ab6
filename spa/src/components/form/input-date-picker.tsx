import { useFormContext, Controller } from "react-hook-form";
import { twMerge } from "tailwind-merge";
import ReactDatePicker, { registerLocale } from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { ptBR } from "date-fns/locale";

registerLocale("pt-BR", ptBR);

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  name: string;
  readOnly?: boolean;
  minDate?: Date | undefined;
  maxDate?: Date | undefined;
}

export function InputDatePicker({
  label,
  name,
  readOnly = false,
  className,
  minDate,
  maxDate,
}: InputProps) {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <div className={twMerge("col-span-full w-full", className)}>
      <label className="mb-1 block text-sm font-medium text-gray-700" htmlFor={label}>
        {label}
      </label>
      <div className="relative  w-full" data-error={!!errors[name]?.message}>
        <Controller
          control={control}
          name={name}
          render={({ field }) => (
            <ReactDatePicker
              dateFormat="dd/MM/yyyy"
              locale="pt-BR"
              onChange={(date) => field.onChange(date)}
              selected={field.value}
              isClearable={!readOnly}
              clearButtonClassName="btn-clear-dt"
              minDate={minDate}
              maxDate={maxDate}
              disabled={readOnly}
              data-error={!!errors[name]?.message}
              className="focus:shadow-outline w-full appearance-none rounded border border-zinc-400 px-3 py-2 leading-tight text-gray-700 read-only:bg-zinc-100 focus:outline-none data-[error=true]:border-red-500"
            />
          )}
        />
      </div>
      {!!errors[name]?.message && (
        <p className="text-xs italic text-red-500">{errors[name]?.message?.toString()}</p>
      )}
    </div>
  );
}
