import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Off } from "lucide-react";
import { ComponentProps, useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { twMerge } from "tailwind-merge";
import ReactCopyToClipboard from "react-copy-to-clipboard";

interface InputProps extends ComponentProps<"input"> {
  name: string;
  label: string;
  readOnly?: boolean;
  copyToClipboard?: boolean;
}

export function InputPassword({
  name,
  label,
  readOnly = false,
  copyToClipboard = false,
  className,
  ...rest
}: InputProps) {
  const {
    register,
    formState: { errors },
    getValues,
  } = useFormContext();

  const [type, setType] = useState("password");
  const [show, setShow] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setShow(false);
    }, 1000 * 2);

    return () => {
      clearTimeout(timeout);
    };
  }, [show]);

  return (
    <div className={twMerge("relative col-span-full w-full", className)}>
      <label
        className="mb-1 block text-sm font-medium text-gray-700"
        htmlFor={label}
      >
        {label}
      </label>
      <input
        id={label}
        readOnly={readOnly}
        {...register(name)}
        autoComplete="off"
        {...rest}
        type={type}
        data-error={!!errors[name]?.message}
        className="focus:shadow-outline w-full appearance-none rounded border border-zinc-400 px-3 py-2 pr-6 leading-tight text-gray-700 read-only:bg-zinc-100 focus:outline-none data-[error=true]:border-red-500"
      />

      {copyToClipboard && (
        <ReactCopyToClipboard
          text={getValues(name)}
          onCopy={() => setShow(true)}
        >
          <span
            title="copiar para área de transferência"
            onClick={(e) => e.stopPropagation()}
          >
            {show && (
              <CopyCheck className="absolute bottom-3 right-8 h-4 w-4 shrink-0 text-green-600" />
            )}
            {!show && (
              <Copy className="absolute bottom-3 right-8 h-4 w-4 shrink-0 cursor-pointer text-zinc-500" />
            )}
          </span>
        </ReactCopyToClipboard>
      )}

      {/* <Copy className="absolute bottom-3 right-12 h-4 w-4" /> */}
      {type === "password" && (
        <Eye
          onClick={() => setType("text")}
          className="absolute bottom-2.5 right-2 h-5 w-5 cursor-pointer text-zinc-500"
        />
      )}
      {type === "text" && (
        <EyeOff
          onClick={() => setType("password")}
          className="absolute bottom-2.5 right-2 h-5 w-5 cursor-pointer"
        />
      )}

      {!!errors[name]?.message && (
        <p className="text-xs italic text-red-500">
          {errors[name]?.message?.toString()}
        </p>
      )}
    </div>
  );
}
