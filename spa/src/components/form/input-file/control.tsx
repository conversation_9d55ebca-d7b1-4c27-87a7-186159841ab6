// @ts-nocheck
import { ComponentProps, useCallback, useEffect } from "react";
import { useInputFile } from "./root";
import { useFormContext } from "react-hook-form";
import { useDropzone } from "react-dropzone";
import { UploadCloud } from "lucide-react";
import { filesize } from "filesize";
import _ from "lodash";

interface ControlProps extends ComponentProps<"input"> {
  name: string;
  maxSizeMB: number;
  acceptFiles?: {
    [key: string]: string[];
  };
}

export function Control({
  name,
  maxSizeMB,
  acceptFiles = undefined,
  ...rest
}: ControlProps) {
  const { id, setDragAccept, setDragReject, setErrors, onFileSelected } =
    useInputFile();
  const {
    register,
    unregister,
    setValue,
    formState: { errors },
  } = useFormContext();

  const onDrop = useCallback(
    (droppedFiles: any) => {
      setValue(name, droppedFiles, { shouldValidate: true });
    },
    [setValue, name],
  );

  const {
    getRootProps,
    getInputProps,
    isFocused,
    isDragAccept,
    isDragReject,
    acceptedFiles,
  } = useDropzone({
    accept: acceptFiles,
    maxFiles: 1,
    multiple: false,
    maxSize: maxSizeMB * 1024 * 1024, // 10MB
    onDrop,
  });

  setDragAccept(isDragAccept);
  setDragReject(isDragReject);
  onFileSelected(acceptedFiles);

  useEffect(() => {
    register(name);
    return () => {
      unregister(name);
    };
  }, [register, unregister, name]);

  const fileSelected = acceptedFiles.map((file: any) => (
    <span key={file.path}>
      {file.path} - {filesize(file.size)}
    </span>
  ));

  function getAcceptedExtensions(obj: any) {
    const exts = Object.keys(obj)
      .map((item) => obj[item])
      .flat();

    return _.uniq(exts).join(" ");
  }

  useEffect(() => {
    setErrors(
      errors[name]?.message ? errors[name]?.message?.toString() : undefined,
    );
  }, [errors, name, setErrors]);

  return (
    <div
      {...getRootProps({ isFocused, isDragAccept, isDragReject })}
      className="flex flex-col items-center justify-center"
    >
      <div className="flex items-center rounded-full border-4 border-zinc-50 bg-zinc-100 p-2 group-hover:border-zinc-200">
        <UploadCloud className="h-5 w-5 text-zinc-600" />
      </div>
      <input
        id={id}
        type="file"
        accept="text/plain"
        className="sr-only"
        {...rest}
        // {...register(name)}
        // onChange={handleSelected}
        {...getInputProps()}
      />
      {fileSelected.length > 0 ? (
        fileSelected
      ) : (
        <>
          <p>Click para selecionar ou Arraste e solte o arquivo aqui</p>
          <small>{getAcceptedExtensions(acceptFiles)}</small>
        </>
      )}
    </div>
  );
}
