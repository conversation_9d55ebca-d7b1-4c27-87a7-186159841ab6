import {
  Component<PERSON><PERSON>,
  create<PERSON>ontext,
  Dispatch,
  ReactNode,
  SetStateAction,
  useContext,
  useId,
  useState,
} from "react";
import { twMerge } from "tailwind-merge";

interface RootProps extends ComponentProps<"div"> {
  label?: string;
  children: ReactNode;
}

type InputFileContextType = {
  id: string;
  files: File[];
  onFileSelected: (files: File[]) => void;
  errors?: string;
  setErrors: Dispatch<SetStateAction<string | undefined>>;
  setDragAccept: Dispatch<SetStateAction<boolean>>;
  setDragReject: Dispatch<SetStateAction<boolean>>;
};
const InputFileContext = createContext({} as InputFileContextType);

export function Root({ children, className, label, ...rest }: RootProps) {
  const id = useId();
  const [files, setFiles] = useState<File[]>([]);
  const [errors, setErrors] = useState<string | undefined>(undefined);

  const [isDragAccept, setDragAccept] = useState<boolean>(false);
  const [isDragReject, setDragReject] = useState<boolean>(false);

  return (
    <InputFileContext.Provider
      value={{
        id,
        files,
        onFileSelected: setFiles,
        errors,
        setErrors,
        setDragAccept,
        setDragReject,
      }}
    >
      <div className={twMerge("col-span-full w-full", className)} {...rest}>
        {label && (
          <p className="mb-1 block text-sm font-medium text-gray-700">
            {label}
          </p>
        )}
        <div
          data-focused={isDragAccept}
          data-reject={isDragReject}
          data-error={!!(errors && files.length === 0)}
          className="group col-span-full flex w-full items-center justify-center rounded-md border py-4 hover:bg-zinc-50 data-[error=true]:border-red-500 data-[focused=true]:border-blue-500 data-[reject=true]:border-red-500"
        >
          {children}
        </div>
        {errors && files.length === 0 && (
          <p className="text-xs italic text-red-500">{errors}</p>
        )}
      </div>
    </InputFileContext.Provider>
  );
}

export const useInputFile = () => useContext(InputFileContext);
