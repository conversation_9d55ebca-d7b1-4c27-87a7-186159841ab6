import { UploadCloud } from "lucide-react";
import { ComponentProps, useMemo } from "react";
import { useInputFile } from "./root";
import { twMerge } from "tailwind-merge";

interface TriggerProps extends ComponentProps<"label"> {}
export function Trigger({ className, ...rest }: TriggerProps) {
  const { id, files } = useInputFile();

  const preview = useMemo(() => {
    if (files.length === 0) {
      return null;
    }

    return files;
  }, [files]);

  return (
    <label
      htmlFor={id}
      className={twMerge(
        "flex flex-1 cursor-pointer flex-col items-center justify-center",
        className,
      )}
      {...rest}
    >
      <div className=" flex items-center rounded-full border-4 border-zinc-50 bg-zinc-100 p-2 group-hover:border-zinc-200">
        <UploadCloud className="h-5 w-5 text-zinc-600" />
      </div>
      <div className="flex flex-col items-center gap-1 text-sm">
        <span className="text-sm">Click para selecionar o arquivo</span>
        <span className="text-xs">
          {preview ? (
            <span>
              arquivo selecionado: <strong>{preview[0].name}</strong>
            </span>
          ) : (
            "arquivo aceito: .txt"
          )}
        </span>
      </div>
    </label>
  );
}
