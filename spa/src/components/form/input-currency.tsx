import { ComponentProps, useCallback, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { twMerge } from "tailwind-merge";
import { maskFormat } from "@/utils";

interface InputProps extends ComponentProps<"input"> {
  name: string;
  label: string;
  readOnly?: boolean;
  mask: "currency" | "number" | "percentage";
}

export function InputCurrency({
  name,
  label,
  readOnly = false,
  className,
  mask,
  ...rest
}: InputProps) {
  const {
    register,
    getValues,
    setValue,
    formState: { errors },
  } = useFormContext();

  useEffect(() => {
    setValue(name, maskFormat[mask](getValues(name)));
  }, [name, mask, setValue, getValues]);

  const handleInput = useCallback(
    (e: React.FormEvent<HTMLInputElement>) => {
      e.currentTarget.value = maskFormat[mask](e.currentTarget.value);
    },
    [mask],
  );

  return (
    <div className={twMerge("col-span-full w-full", className)}>
      <label
        className="mb-1 block text-sm font-medium text-gray-700"
        htmlFor={label}
      >
        {label}
      </label>
      <input
        id={label}
        readOnly={readOnly}
        onInput={handleInput}
        {...register(name)}
        autoComplete="off"
        {...rest}
        data-error={!!errors[name]?.message}
        className="focus:shadow-outline w-full appearance-none rounded border border-zinc-400 px-3 py-2 leading-tight text-gray-700 read-only:bg-zinc-100 focus:outline-none data-[error=true]:border-red-500"
      />
      {!!errors[name]?.message && (
        <p className="text-xs italic text-red-500">
          {errors[name]?.message?.toString()}
        </p>
      )}
    </div>
  );
}
