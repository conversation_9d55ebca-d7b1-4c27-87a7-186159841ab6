import ReactAsyncSelect from "react-select/async";
import { StylesConfig } from "react-select";
import { useFormContext, Controller } from "react-hook-form";
import { twMerge, ClassNameValue } from "tailwind-merge";

interface SelectProps {
  label: string;
  name: string;
  isMulti?: boolean;
  isClearable?: boolean;
  readOnly?: boolean;
  placeholder?: string;
  className?: ClassNameValue;
  loadOptions: (inputValue: any, callback: any) => Promise<any>;
}

export function SelectAsync({
  label,
  name,
  isMulti = false,
  isClearable = true,
  readOnly = false,
  className,
  placeholder,
  loadOptions,
  ...rest
}: SelectProps) {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const style: StylesConfig<any, any> = {
    option: (styles, { isDisabled, isFocused }) => ({
      ...styles,
      color: "#333",
      // "text-transform": "uppercase",
      backgroundColor: isFocused ? "rgba(0,0,0,0.1)" : undefined,
      ":active": {
        ...styles[":active"],
        backgroundColor: !isDisabled ? "rgba(0,0,0,0.3)" : undefined,
      },
    }),
    control: (base: any) => ({
      ...base,
      border: 0,
      background: "transparent",
      boxShadow: "none",
      fontSize: "1rem",
      fontWeight: 400,
    }),
    container: (base: any) => ({
      ...base,
      width: "100%",
      borderColor: "#333",
      paddingLeft: "0",
    }),
  };

  return (
    <div className={twMerge("col-span-full w-full", className)} {...rest}>
      <label
        className="mb-1 block text-sm font-medium text-gray-700"
        htmlFor={label}
      >
        {label}
      </label>
      <div
        className="focus:shadow-outline h-[38px] w-full appearance-none rounded border border-zinc-400 leading-tight focus:outline-none data-[error=true]:border-red-500"
        data-error={!!errors[name]?.message}
      >
        <Controller
          control={control}
          name={name}
          render={({ field }) => (
            <ReactAsyncSelect
              isClearable={isClearable}
              isMulti={isMulti}
              styles={style}
              isDisabled={readOnly}
              classNamePrefix="react-select"
              placeholder={placeholder}
              loadOptions={loadOptions}
              loadingMessage={() => "carregando..."}
              noOptionsMessage={() => "digite para pesquisar..."}
              {...field}
            />
          )}
        />
      </div>
      {!!errors[name]?.message && (
        <p className="text-xs italic text-red-500">
          {errors[name]?.message?.toString()}
        </p>
      )}
    </div>
  );
}
