import { ComponentProps } from "react";
import { useFormContext } from "react-hook-form";
import { twMerge } from "tailwind-merge";

interface InputProps extends ComponentProps<"input"> {
  name: string;
}

export function Field({ name, className, ...rest }: InputProps) {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <input
      {...register(name)}
      {...rest}
      data-error={!!errors[name]?.message}
      className={twMerge(
        "appearance-none border data-[error=true]:border-red-500 rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",
        className,
      )}
    />
  );
}
