import { ComponentProps } from "react";
import { useFormContext } from "react-hook-form";
import { twMerge } from "tailwind-merge";

interface InputProps extends ComponentProps<"input"> {
  label: string;
  name: string;
  options: { label: string; value: string }[];
  readOnly?: boolean;
}

export function InputRadio({
  label,
  name,
  readOnly = false,
  options,
  className,
}: InputProps) {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <div className={twMerge("col-span-full flex flex-1 flex-col", className)}>
      <div className="mb-1 block text-sm font-medium text-gray-700">
        {label}
      </div>
      <div
        className="focus:shadow-outline flex w-full appearance-none gap-2 overflow-hidden rounded border border-zinc-400 px-2 py-2 leading-tight focus:outline-none data-[error=true]:border-red-500"
        data-error={!!errors[name]?.message}
      >
        {options.map((option) => (
          <div key={option.value} className="flex items-center gap-1">
            <input
              id={`radio-${option.value}`}
              type="radio"
              className="relative float-left h-4 w-4 appearance-none rounded-full border-2 border-solid border-neutral-300 before:pointer-events-none before:absolute before:h-4 before:w-4 before:scale-0 before:rounded-full before:bg-transparent before:opacity-0 before:shadow-[0px_0px_0px_13px_transparent] before:content-[''] after:absolute after:z-[1] after:block after:h-4 after:w-4 after:rounded-full after:content-[''] checked:border-primary checked:before:opacity-[0.16] checked:after:absolute checked:after:left-1/2 checked:after:top-1/2 checked:after:h-[0.625rem] checked:after:w-[0.625rem] checked:after:rounded-full checked:after:border-primary checked:after:bg-primary checked:after:content-[''] checked:after:[transform:translate(-50%,-50%)] hover:cursor-pointer hover:before:opacity-[0.04] hover:before:shadow-[0px_0px_0px_13px_rgba(0,0,0,0.6)] dark:border-neutral-600 dark:checked:border-primary dark:checked:after:border-primary dark:checked:after:bg-primary"
              value={option.value}
              readOnly={readOnly}
              {...register(name)}
            />
            <label htmlFor={`radio-${option.value}`}>{option.label}</label>
          </div>
        ))}
      </div>
      {!!errors[name]?.message && (
        <p className="text-xs italic text-red-500">
          {errors[name]?.message?.toString()}
        </p>
      )}
    </div>
  );
}
