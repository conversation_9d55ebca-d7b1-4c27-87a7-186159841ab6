// @ts-nocheck
import { Loader2 } from "lucide-react";
import { Controller, useFormContext } from "react-hook-form";
import ReactInputMask from "react-input-mask";
import { twMerge } from "tailwind-merge";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  name: string;
  readOnly?: boolean;
  mask: string;
  isLoading?: boolean;
}

export function InputMask({
  label,
  name,
  isLoading = false,
  readOnly = false,
  className,
  mask,
  ...rest
}: InputProps) {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <div className={twMerge("col-span-full w-full", className)}>
      <label
        className="mb-1 block text-sm font-medium text-primary/70"
        htmlFor={label}
      >
        {label}
      </label>
      <div className="relative  w-full" data-error={!!errors[name]?.message}>
        <Controller
          control={control}
          name={name}
          render={({ field: { onChange, onBlur, value, name, ref } }) => (
            <ReactInputMask
              onBlur={onBlur}
              onChange={onChange}
              value={value}
              inputRef={ref}
              mask={mask}
              {...rest}
              readOnly={readOnly}
              data-error={!!errors[name]?.message}
              className="focus:shadow-outline w-full appearance-none rounded border border-zinc-400 bg-transparent px-3 py-2 leading-tight text-primary placeholder:text-sm read-only:bg-zinc-100 focus:outline-none data-[error=true]:border-red-500"
            />
          )}
        />
        {isLoading && (
          <Loader2 className="absolute right-2.5 top-3 h-4 w-4 animate-spin" />
        )}
      </div>
      {!!errors[name]?.message && (
        <p className="text-xs italic text-red-500">
          {errors[name]?.message?.toString()}
        </p>
      )}
    </div>
  );
}
