import { ComponentProps } from "react";
import ReactSelect, {
  OptionsOrGroups,
  GroupBase,
  StylesConfig,
} from "react-select";
import { useFormContext, Controller } from "react-hook-form";
import { twMerge } from "tailwind-merge";

interface SelectProps extends ComponentProps<"select"> {
  label: string;
  name: string;
  options: OptionsOrGroups<any, GroupBase<any>>;
  isMulti?: boolean;
  isClearable?: boolean;
  readOnly?: boolean;
}

export const style: StylesConfig<any, any> = {
  option: (styles, { isDisabled, isFocused }) => ({
    ...styles,
    color: "#333",
    backgroundColor: isFocused ? "rgba(0,0,0,0.1)" : undefined,
    ":active": {
      ...styles[":active"],
      backgroundColor: !isDisabled ? "rgba(0,0,0,0.3)" : undefined,
    },
  }),
  control: (base: any) => ({
    ...base,
    border: 0,
    background: "transparent",
    boxShadow: "none",
  }),
  container: (base: any) => ({
    ...base,
    width: "100%",
    borderColor: "#333",
    paddingLeft: "0",
  }),
};

export function Select({
  label,
  name,
  options,
  isMulti = false,
  isClearable = true,
  readOnly = false,
  className,
}: SelectProps) {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <div className={twMerge("col-span-full w-full", className)}>
      <label
        className="mb-1 block text-sm font-medium text-foreground"
        htmlFor={label}
      >
        {label}
      </label>
      <div
        className="focus:shadow-outline w-full appearance-none rounded border border-zinc-400 leading-tight focus:outline-none data-[error=true]:border-red-500"
        data-error={!!errors[name]?.message}
      >
        <Controller
          control={control}
          name={name}
          render={({ field }) => (
            <ReactSelect
              isClearable={isClearable}
              isMulti={isMulti}
              styles={style}
              isDisabled={readOnly}
              classNamePrefix="react-select"
              placeholder="Selecione"
              {...field}
              options={options}
            />
          )}
        />
      </div>
      {!!errors[name]?.message && (
        <p className="text-xs italic text-red-500">
          {errors[name]?.message?.toString()}
        </p>
      )}
    </div>
  );
}
