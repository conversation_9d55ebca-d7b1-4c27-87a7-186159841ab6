import { useStoreAuth } from "@/states";
import { getCookie } from "@/utils";
import { TriangleAlert } from "lucide-react";
import { useEffect, useState } from "react";
import { jwtDecode } from "jwt-decode";

export function ExpirationSessionAlert() {
    const token = getCookie("access_token");
    const { refresh } = useStoreAuth()
    const [showTokenWarning, setShowTokenWarning] = useState(false);
    const [countdown, setCountdown] = useState<number>(0);

    useEffect(() => {
        if (!token) return;

        const checkTokenExpiration = () => {
            try {
                const decoded = jwtDecode(token);

                if (decoded?.exp) {
                    const now = Math.floor(Date.now() / 1000);
                    const expiresIn = decoded.exp - now;

                    if (expiresIn <= 90 && expiresIn > 0) {
                        setShowTokenWarning(true);
                        setCountdown(expiresIn);
                    } else {
                        //setShowTokenWarning(false);
                    }
                }
            } catch (error) {
                console.error("Erro ao decodificar token:", error);
            }
        };


        checkTokenExpiration();


        const checkInterval = setInterval(checkTokenExpiration, 10000);
        let countdownInterval: NodeJS.Timeout;

        if (showTokenWarning) {
            countdownInterval = setInterval(() => {
                setCountdown(prev => {
                    if (prev <= 1) {
                        clearInterval(countdownInterval);
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);
        }

        return () => {
            clearInterval(checkInterval);
            if (countdownInterval) clearInterval(countdownInterval);
        };
    }, [token, showTokenWarning]);


    if (!showTokenWarning) return null;


    return (
        <div onClick={() => refresh()} className="fixed bottom-4 right-4 bg-yellow-500 text-white px-4 py-2 rounded-md shadow-lg z-50 animate-pulse cursor-pointer">
            <div className="flex items-center gap-2">
                <TriangleAlert className="text-yellow-800" />
                <div>
                    <p className="font-medium">Sessão expirando!</p>
                    <p className="text-sm">
                        Sua sessão expirará em <span className="font-bold">{countdown}</span> segundos
                    </p>
                    <p className="text-sm">click aqui para renovar.</p>
                </div>

            </div>
        </div>
    )

}