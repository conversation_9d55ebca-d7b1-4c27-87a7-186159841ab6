import { Button } from "@/components/ui/button";
import { ChevronFirst, ChevronLast, ChevronLeft, ChevronRight } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useQueryState } from "nuqs";

interface PaginateProps {
  metadata: {
    totalPages: number;
  };
}

export function Paginate({ metadata }: PaginateProps) {
  const [page, setPage] = useQueryState("page", { defaultValue: "1" });

  function handleChangePage(e: React.ChangeEvent<HTMLInputElement>) {
    const value = Number(e.target.value);
    if (!isNaN(value)) {
      if (value < 1) {
        setPage("1");
      } else if (value > metadata?.totalPages) {
        setPage(metadata?.totalPages?.toString() || "1");
      } else {
        setPage(value.toString());
      }
    }
  }

  function nextPage() {
    const nextPage = Number(page) + 1;
    if (nextPage <= metadata?.totalPages) {
      setPage(nextPage.toString());
    }
  }

  function previousPage() {
    const previousPage = Number(page) - 1;
    if (previousPage >= 1) {
      setPage(previousPage.toString());
    }
  }

  return (
    <div className="text-sm flex items-center gap-1">
      <Button variant="ghost" size="icon" onClick={() => setPage("1")}>
        <ChevronFirst />
      </Button>
      <Button variant="ghost" size="icon" onClick={previousPage}>
        <ChevronLeft />
      </Button>
      <Input
        type="number"
        defaultValue={page}
        value={page}
        min={1}
        step={1}
        max={metadata?.totalPages}
        className="w-12 h-8 p-0 text-center focus-visible:ring-0 focus-visible:ring-offset-0 ml-2"
        onChange={handleChangePage}
      />
      <span className="text-muted-foreground mr-2">de {metadata?.totalPages}</span>
      <Button variant="ghost" size="icon" onClick={nextPage}>
        <ChevronRight />
      </Button>
      <Button variant="ghost" size="icon" onClick={() => setPage(metadata?.totalPages?.toString())}>
        <ChevronLast />
      </Button>
    </div>
  );
}
