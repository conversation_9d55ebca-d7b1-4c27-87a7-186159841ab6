import { ColumnDef, flexRender, getCoreRowModel, useReactTable, Row } from "@tanstack/react-table";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Loader2 } from "lucide-react";
import { twMerge } from "tailwind-merge";
import { parseAsInteger, useQueryState } from "nuqs";
import { debounce } from "lodash";
import React from "react";
import { useQuery } from "@tanstack/react-query";
import { useHttp } from "@/hooks";
import { Paginate } from "./paginate";

interface DataTableProps<TData, TValue> {
  url: string;
  placeholder?: string;
  columns: ColumnDef<TData, TValue>[];
  handleRowClick?: (row: Row<TData>) => void;
}

export function DataTableAsync<TData, TValue>({
  url,
  placeholder = "Filtrar por: Nome",
  columns,
  handleRowClick,
}: DataTableProps<TData, TValue>) {
  const [q, setQ] = useQueryState("q", { defaultValue: "" });
  const [page, setPage] = useQueryState("page", { defaultValue: "1" });
  const [pageSize, setPageSize] = useQueryState("pageSize", parseAsInteger.withDefault(20));
  const { httpGet } = useHttp();

  const query = useQuery({
    queryKey: [url, page, q],
    queryFn: async () => {
      const { data } = await httpGet(url, { params: { page, pageSize, q } });
      return data ? data : [];
    },
  });

  const { data, isLoading } = query;
  const metadata = data?.meta;

  const table = useReactTable({
    data: data?.data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const statusItems = React.useMemo(() => {
    if (!metadata) {
      return "0 items";
    }
    const x1 = (metadata.currentPage * pageSize - pageSize + 1).toString().padStart(2, "0");
    const x2 = (metadata.currentPage * pageSize).toString().padEnd(2, "0");

    return `${x1}-${x2} de ${metadata.totalRecords} items`;
  }, [metadata, pageSize]);

  function handleChangeSearch(e: React.ChangeEvent<HTMLInputElement>) {
    setPage("1");
    setQ(e.target.value);
  }

  function handleChangePageSize(value: string) {
    setPage("1");
    setPageSize(parseInt(value));
  }

  return (
    <div className="w-full h-full flex flex-col gap-2">
      <div className="flex items-center gap-4">
        <Input
          placeholder={placeholder}
          type="search"
          defaultValue={q}
          onChange={debounce(handleChangeSearch, 500)}
          className="max-w-sm focus-visible:ring-0 focus-visible:ring-offset-0"
        />
      </div>
      <ScrollArea className="h-[calc(100vh-11rem)] w-[full] rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading && (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  <div className="flex items-center justify-center">
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Carregando aguarde
                  </div>
                </TableCell>
              </TableRow>
            )}
            {!isLoading && data?.data && table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  onClick={handleRowClick ? () => handleRowClick(row) : undefined}
                  className={twMerge(handleRowClick ? "cursor-pointer" : "")}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <>
                {!isLoading && (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      Sem resultados.
                    </TableCell>
                  </TableRow>
                )}
              </>
            )}
          </TableBody>
        </Table>
      </ScrollArea>
      <div className="flex justify-between py-2">
        <div className="flex items-center gap-2">
          <p className="text-sm text-muted-foreground py-2">items por página</p>
          <Select defaultValue={pageSize.toString()} onValueChange={handleChangePageSize}>
            <SelectTrigger className="w-14 h-8 p-0 px-2">
              <SelectValue placeholder="Theme" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground py-2">{statusItems}</p>
        </div>

        <Paginate metadata={metadata} />
      </div>
    </div>
  );
}
