import { ReactNode, useEffect, useState } from "react";
import { Trash2 } from "lucide-react";
import { twMerge } from "tailwind-merge";
import { Loader2 } from "lucide-react";

interface Props extends React.InputHTMLAttributes<HTMLDivElement> {
  disable?: boolean;
  children?: ReactNode;
  onAction: () => void;
  alterText?: string;
}

export function TrashConfirmButton({ onAction, children, className, alterText, ...rest }: Props) {
  const [hidden, setHidden] = useState(true);
  const [isLoading, setLoading] = useState(false);

  function handleConfirm() {
    setHidden(false);
  }

  function handleClick() {
    setLoading(true);
    onAction();
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      setHidden(true);
      setLoading(false);
    }, 1000 * 5);
    return () => clearTimeout(timer);
  }, [hidden]);

  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
      }}
      className={twMerge(
        "flex cursor-pointer items-center gap-2 text-sm text-foreground/70",
        className
      )}
      {...rest}
    >
      {!hidden && (
        <div onClick={handleClick} className="flex items-center gap-2">
          {!isLoading && <Trash2 className="h-4 w-4 text-red-500" />}
          {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
          {alterText ? alterText : children}
        </div>
      )}

      {hidden && (
        <div onClick={handleConfirm} className="flex items-center gap-2">
          <Trash2 className="h-4 w-4 text-foreground/70 hover:text-foreground" />
          {children}
        </div>
      )}
    </div>
  );
}
