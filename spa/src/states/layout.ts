import { create } from "zustand";

interface State {
  showMobileMenu: boolean;
  hasControlArea: boolean;
  isLoading: boolean;
  toggleShowMobileMenu: () => void;
  setHasControlArea: (payload: boolean) => void;
  setIsLoading: (payload: boolean) => void;
}

export const useLayoutState = create<State>((set) => ({
  showMobileMenu: false,
  hasControlArea: false,
  isLoading: false,
  toggleShowMobileMenu: () => set((state) => ({ showMobileMenu: !state.showMobileMenu })),
  setHasControlArea: (payload: boolean) => set(() => ({ hasControlArea: payload })),
  setIsLoading: (payload: boolean) => set(() => ({ isLoading: payload })),
}));
