import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { setCookie, getCookie } from "@/utils";
import { User } from "@/types";

interface State {
  data: string[];
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  user: User | null;
  signIn: (user: string, password: string) => Promise<boolean>;
  refresh: () => Promise<void>;
  getMe: () => Promise<User | null>;
  logout: () => void;
  setUser: (user: User | null) => void;
  can: (acl: string[] | string) => boolean;
}

export const useStoreAuth = create(
  persist<State>(
    (set, get) => ({
      data: [],
      isAuthenticated: false,
      loading: false,
      error: null,
      user: null,
      getMe: async () => {
        const response = await fetch(`${import.meta.env.VITE_API_URL}/me`, {
          headers: {
            Authorization: `Bearer ${getCookie("access_token")}`,
          },
        });

        if (response.status === 401) {
          set({ data: [], isAuthenticated: false, loading: false });
          return false;
        }

        const user = await response.json();
        set({ user, isAuthenticated: true, loading: false });
        return user;
      },
      signIn: async (user: string, password: string) => {
        set({ loading: true, error: null });
        console.log("🔐 Iniciando login...");
        console.log("📍 URL da API:", import.meta.env.VITE_API_URL);
        console.log("👤 Usuário:", user);

        try {
          const apiUrl = `${import.meta.env.VITE_API_URL}/auth/sign-in`;
          console.log("🌐 URL completa:", apiUrl);

          const response = await fetch(apiUrl, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ user, password }),
          });

          console.log("📡 Response recebida:", response);
          console.log("📊 Status:", response.status);
          console.log("📋 Headers:", [...response.headers.entries()]);

          const data = await response.json();
          console.log("📦 Dados recebidos:", data);

          if (response.status === 201) {
            setCookie("access_token", data.access_token, { expires: 7 });
            setCookie("refresh_token", data.refresh_token, { expires: 7 });
            set({ data, isAuthenticated: true, loading: false });
            return true;
          }

          if (response.status === 429) {
            alert("Você atingiu o limite de tentativas. Por favor, tente novamente mais tarde.");
            set({ data: [], isAuthenticated: false, loading: false });
            return false;
          }

          set({ data: [], isAuthenticated: false, loading: false });
          return false;
        } catch (error: any) {
          console.error("❌ Erro capturado:", error);
          console.error("❌ Tipo do erro:", error.name);
          console.error("❌ Mensagem:", error.message);
          console.error("❌ Stack:", error.stack);

          if (error?.message.includes("Failed to fetch")) {
            console.error("🚫 Erro de fetch detectado");
            alert(
              "Não foi possível conectar ao servidor. Por favor, verifique sua conexão ou tente novamente mais tarde."
            );
          }

          console.error("Erro ao carregar dados:", error);
          set({ error: "Erro ao carregar dados", isAuthenticated: false, loading: false });
          return false;
        }
      },
      refresh: async () => {
        try {
          const refresh_token = getCookie("refresh_token");
          if (!refresh_token) return;

          const response = await fetch(`${import.meta.env.VITE_API_URL}/auth/refresh`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ refresh_token }),
          });
          const data = await response.json();

          if (response.status === 201) {
            setCookie("access_token", data.access_token, { expires: 7 });
            setCookie("refresh_token", data.refresh_token, { expires: 7 });
            set({ data, isAuthenticated: true, loading: false });
          }
        } catch (error) {
          alert("Não foi renovar a sessão. Por favor, faça login novamente.");
          console.error("Erro ao renovar sessão:", error);
        }
      },
      setUser: (user: User | null) => {
        set({ user });
      },
      logout: () => {
        set({ data: [], isAuthenticated: false });
        localStorage.removeItem("accessToken");
        localStorage.removeItem("refreshToken");
      },
      can: (acl: string[] | string): boolean => {
        if (!useStoreAuth.getState().user) return false;
        const _acl = Array.isArray(acl) ? ["admin", ...acl] : ["admin", acl];

        const user = get().user;
        return user ? _acl.some((slug) => user.acl.includes(slug)) : false;
      },
    }),
    {
      name: "auth-storage",
      storage: createJSONStorage(() => localStorage),
    }
  )
);
