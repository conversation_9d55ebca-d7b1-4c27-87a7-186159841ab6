import { Chat } from "./chat";
export interface IncomingMessage {
  id: string;
  phone: string;
  message_id: string;
  reference_message_id: string;
  message: string;
  type: string;
  status: string;
  payload: Payload;
  chat_id: string;
  chat: Chat;
  momment: Date;
}

interface Text {
  message: string;
}

interface Payload {
  text: Text;
  type: string;
  phone: string;
  photo: string;
  fromMe: boolean;
  isEdit: boolean;
  status: string;
  chatLid: string;
  fromApi: boolean;
  isGroup: boolean;
  momment: number;
  chatName: string;
  broadcast: boolean;
  forwarded: boolean;
  messageId: string;
  instanceId: string;
  senderName: string;
  senderPhoto: any;
  isNewsletter: boolean;
  isStatusReply: boolean;
  connectedPhone: string;
  participantLid: any;
  waitingMessage: boolean;
}
