export type InstanceStatus = {
  status: {
    connected: boolean;
    session: boolean;
    created: number;
    error: string;
    smartphoneConnected: boolean;
  };
  device: {
    phone: string;
    imgUrl: string;
    name: string;
    device: {
      sessionName: string;
      device_model: string;
    };
    originalDevice: string;
    sessionId: number;
    isBusiness: boolean;
  };
  me: {
    receivedAndDeliveryCallbackUrl: string;
    created: string;
    autoReadMessage: boolean;
    callRejectAuto: boolean;
    token: string;
    connected: boolean;
    receiveCallbackSentByMe: boolean;
    deliveryCallbackUrl: string;
    due: number;
    name: string;
    id: string;
    receivedCallbackUrl: string;
    paymentStatus: string;
  };
};
