import { isValid } from "date-fns";

export function validateDate(value?: string, required?: boolean): boolean {
  if (!required && !value) {
    return true;
  }

  if (!value || value.length < 10) {
    return false;
  }
  const [day, month, year] = value.split("/");
  if (day.length === 2 && month.length === 2 && year.length === 4) {
    return isValid(new Date(`${year}-${month}-${day}`));
  }
  return false;
}
