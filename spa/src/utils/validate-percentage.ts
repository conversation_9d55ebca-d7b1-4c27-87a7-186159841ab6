export function validatePercentage(
  payload?: string | null,
  option?: { isEmpty?: boolean },
): boolean {
  // const match = payload.match(/\b(?<!\,)(?!0+(?:\,+)?%)(?:\d|[1-9]\d|100)(?:(?<!100)\,\d+)?%/g);
  if (!payload) {
    return false;
  }

  if (payload === "%" || payload === "% 0") {
    return true;
  }

  const match = payload
    .replace(/% /g, "")
    .match(/^((100)|(\d{1,2}(,\d{1,2})?))/g);

  if (option && option.isEmpty && !payload) {
    return true;
  }

  if (!match) {
    return false;
  }

  const isNumber = Number(
    String(payload)
      .replace(/,/g, ".")
      .replace(/[%|\s]/g, ""),
  );

  if (isNumber === 0 || isNumber < -99 || isNumber > 100) {
    return false;
  }
  return true;
}
