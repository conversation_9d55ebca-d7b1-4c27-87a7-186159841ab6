import Cookies from "js-cookie";
import pkg from "../../package.json";

const _options: Cookies.CookieAttributes = {
  expires: 1, // 1 dia
  path: "/",
  secure: false, // true = o cookie só pode ser enviado através de HTTPS
  sameSite: "strict",
  httpOnly: false, // true = o cookie não pode ser acessado através de JavaScript
};

export function setCookie(
  name: string,
  value: string | object,
  options?: Cookies.CookieAttributes
) {
  Cookies.set(`${pkg.name}.${name}`, typeof value === "object" ? JSON.stringify(value) : value, {
    ..._options,
    ...options,
  });
}

export function getCookie(name: string) {
  const cookie = Cookies.get(`${pkg.name}.${name}`);

  if (cookie?.match(/^\s*\{[\s\S]*\}\s*$/gm)) {
    return JSON.parse(cookie);
  }

  return cookie ? String(cookie) : null;
}

export function deleteCookies(payload: string | string[]): void {
  if (typeof payload === "object" && payload.length > 0) {
    for (const name of payload) {
      Cookies.remove(`${pkg.name}.${name}`, { path: "/" });
    }

    return;
  }

  if (typeof payload === "string") {
    Cookies.remove(`${pkg.name}.${payload}`, { path: "/" });
  }
}
