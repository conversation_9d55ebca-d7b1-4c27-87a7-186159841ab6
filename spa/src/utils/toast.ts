import { toast as reactToast, ToastOptions } from "react-toastify";

const options: ToastOptions = {
  theme: "colored",
};

export const toast = {
  success(message: string, customOptions?: ToastOptions) {
    reactToast.success(message, { ...options, ...customOptions });
  },
  error(message: string, customOptions?: ToastOptions) {
    reactToast.error(message, { ...options, ...customOptions });
  },
  info(message: string, customOptions?: ToastOptions) {
    reactToast.info(message, { ...options, ...customOptions });
  },
};
