export const maskFormat = {
  currency(payload: string) {
    const value = payload || "";

    return (
      "R$ " +
      value
        .replace(/\D/g, "")
        .replace(/(\d)(\d{2})$/, "$1,$2")
        .replace(/(?=(\d{3})+(\D))\B/g, ".")
    );
  },

  percentage(payload: string) {
    const value = payload || "";

    return (
      "% " +
      value
        .replace(/\D/g, "")
        .replace(/(\d)(\d{2})$/, "$1,$2")
        .replace(/(?=(\d{3})+(\D))\B/g, ".")
    );
  },

  number(payload: string) {
    const value = payload || "";
    return value
      .replace(/\D/g, "")
      .replace(/(\d)(\d{2})$/, "$1,$2")
      .replace(/(?=(\d{3})+(\D))\B/g, ".");
  },
};
