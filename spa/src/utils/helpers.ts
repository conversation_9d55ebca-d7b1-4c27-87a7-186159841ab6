import { get<PERSON><PERSON>ie } from ".";
import byte_Size from "byte-size";

interface cropperProps {
  text: string;
  size: number;
  position?: "center" | "end";
}

export const helpers = {
  document(document?: string, natural?: boolean): string {
    const _document = document || "";
    const _natural = natural || false;
    if (_natural)
      return _document.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, "$1.$2.$3-$4");
    return _document.replace(
      /(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/g,
      "$1.$2.$3/$4-$5",
    );
  },
  cpf(cpf: string): string {
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, "$1.$2.$3-$4");
  },
  cep(cep: string): string {
    const aux = cep || "";
    return aux.replace(/(\d{5})(\d{3})/g, "$1-$2");
  },
  phone(phone: string): string {
    const aux = phone || "";
    return aux.replace(/(\d{2})(\d{5})(\d{4})/g, "($1) $2-$3");
  },
  colSpanClass(colSpan = ""): string {
    const mediasQuery = colSpan.split(",");
    const breakpoints = ["", "md:", "lg:", "xl:", "2xl:"];
    let colSpanClass = "";

    for (let i = 0; i < mediasQuery.length; i++) {
      colSpanClass += `${breakpoints[i]}col-span-${mediasQuery[i]} `;
    }

    return colSpanClass;
  },
  urlAuth(url: string): string {
    //check if url has query params
    if (url.indexOf("?") > -1) {
      return `${url}token=${getCookie("token")}`;
    }
    return `${url}?token=${getCookie("token")}`;
  },
  byteSize(bytes: number): string {
    const { value, unit } = byte_Size(bytes);
    return `${value} ${unit}`;
  },
  cropper({ text = "", size, position = "end" }: cropperProps): string {
    if (!text) {
      return "";
    }
    if (text.length > size) {
      if (position && position === "center") {
        const medium = Math.floor(size / 2);
        const start = text.length - medium + 5;
        return (
          text.substring(0, medium) +
          " ... " +
          text.substring(start, text.length)
        );
      }

      return text.substring(0, size - 4) + " ...";
    }
    return text;
  },
};
