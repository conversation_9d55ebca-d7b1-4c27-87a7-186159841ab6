import { get<PERSON><PERSON><PERSON> } from ".";

function serialize(obj: Record<string, any>): string {
  const str = [];
  for (const p in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, p)) {
      str.push(`${encodeURIComponent(p)}=${encodeURIComponent(obj[p])}`);
    }
  }

  /* eslint-disable no-prototype-builtins */
  for (const p in obj)
    if (obj.hasOwnProperty(p)) {
      str.push(
        encodeURIComponent(p) + "=" + encodeURIComponent(obj[p] as string),
      );
    }

  return str.join("&");
}

export const parse = {
  document(document?: string, natural?: boolean): string {
    const _document = document || "";
    const _natural = natural || false;
    if (_natural)
      return _document.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, "$1.$2.$3-$4");
    return _document.replace(
      /(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/g,
      "$1.$2.$3/$4-$5",
    );
  },
  cpf(cpf: string): string {
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, "$1.$2.$3-$4");
  },
  cep(cep: string): string {
    return cep.replace(/(\d{5})(\d{3})/g, "$1-$2");
  },
  date(value: string): string {
    return value
      .replace(/\D/g, "")
      .replace(/(\d{2})(\d{2})(\d{4})/g, "$3-$2-$1");
  },
  dateTime(payload: string): string {
    const value = payload.padEnd(14, "0");
    return value
      .replace(/\D/g, "")
      .replace(/(\d{2})(\d{2})(\d{4})(\d{2})(\d{2})/g, "$3-$2-$1 $4:$5");
  },

  percentage(payload?: string | number) {
    if (!payload) return "";
    const number = String(payload).match(/%/)
      ? String(payload).replace(/,/g, ".").replace("%", "")
      : Number(String(payload).replace(/\D/g, "")) / 100;

    return `${Intl.NumberFormat("pt-BR", { currency: "BRL" }).format(
      +number,
    )}%`;
  },

  currency(value: number): string {
    if (!value) return "";
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  },

  decimalValue(payload: string | number) {
    const sanitized = String(payload).replace(/\D/g, "");
    return Number(sanitized) / 100;
  },

  number(number: string | number, padStart = 2): string {
    return String(number).padStart(padStart, "0");
  },

  signedUrl(uri: string, obj?: object): string {
    const token = getCookie("token");

    if (obj) {
      return `${import.meta.env.VITE_API_URL}/${uri}?token=${token}&${serialize(
        obj,
      )}`;
    }

    return `${import.meta.env.VITE_API_URL}/${uri}?token=${token}`;
  },
};
