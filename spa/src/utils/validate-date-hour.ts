import { isValid } from "date-fns";

export function validateDateHour(value?: string, required?: boolean): boolean {
  if (!required && !value) {
    return true;
  }

  if (!value || value.length < 10) {
    return false;
  }

  const [day, month, rest] = value.split("/");
  const [year, time] = rest.split(" ");
  const [hour, minute] = value.length > 10 ? time.split(":") : ["00", "00"];
  if (day.length === 2 && month.length === 2 && year.length === 4) {
    return isValid(new Date(`${year}-${month}-${day} ${hour}:${minute}`));
  }
  return false;
}
