import axios, { HeadersDefaults, AxiosError } from "axios";
import { setCookie, getCookie, deleteCookies } from "../utils";

export interface CommonHeaderProperties extends HeadersDefaults {
  Authorization: string;
}

interface failedRequestsQueueProps {
  onSuccess: (token: string) => void;
  onFailure: (err: AxiosError) => void;
}

let isRefreshing = false;
let failedRequestsQueue: failedRequestsQueueProps[] = [];

const token = getCookie("access_token");

export const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  headers: {
    Authorization: `Bearer ${token}`,
    withCredentials: true, /// Enable sending cookies
    accessControlAllowOrigin: "*",
    accessControlAllowHeader: "Origin, X-Requested-With, Content-Type, Accept",
  },
  timeout: 200000,
});

api.interceptors.request.use((request) => {
  const token = getCookie("access_token");
  if (token) request.headers.Authorization = `Bearer ${token}`;
  return request;
});

api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error: AxiosError<any, any>) => {
    if (error.response?.status === 401) {
      if (error.response.data?.name === "TokenExpiredError") {
        const refreshToken = getCookie("refresh_token");
        const originalConfig = error.config;

        if (!isRefreshing) {
          isRefreshing = true;

          api
            .post("/auth/refresh", {
              refreshToken,
            })
            .then((response) => {
              const { token, refreshToken } = response.data;

              setCookie("token", token);
              setCookie("refreshToken", refreshToken);

              api.defaults.headers.options = {
                Authorization: `Bearer ${token}`,
              };

              failedRequestsQueue.forEach((request) => request.onSuccess(token));
              failedRequestsQueue = [];
            })
            .catch((err: AxiosError) => {
              deleteCookies(["token", "refreshToken"]);
              failedRequestsQueue.forEach((request) => request.onFailure(err));
              failedRequestsQueue = [];

              window.location.href = "/login";
            })
            .finally(() => {
              isRefreshing = false;
            });
        }

        return new Promise((resolve, reject) => {
          failedRequestsQueue.push({
            onSuccess: (token: string) => {
              if (originalConfig?.headers?.Authorization) {
                originalConfig.headers.Authorization = `Bearer ${token}`;
                resolve(api(originalConfig));
              }
            },
            onFailure: (err: AxiosError) => {
              reject(err);
            },
          });
        });
      } else {
        if (typeof window !== "undefined") {
          deleteCookies(["token", "refreshToken"]);
        } else {
          return Promise.reject(new Error("Error with authentication token."));
        }
      }
    }

    return Promise.reject(error);
  }
);

export const isNetworkError = (err: AxiosError): boolean => !!err.isAxiosError && !err.response;
