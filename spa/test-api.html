<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste API</title>
</head>
<body>
    <h1>Teste de Conexão com a API</h1>
    <button onclick="testLogin()">Testar Login (Fetch)</button>
    <button onclick="testLoginXHR()">Testar Login (XMLHttpRequest)</button>
    <button onclick="testApiHealth()">Testar Saúde da API</button>
    <button onclick="testExactAuth()">🔥 Testar EXATAMENTE como useStoreAuth</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testando...';

            console.log('Iniciando teste de login...');
            console.log('URL da API:', 'http://localhost:3333/auth/sign-in');

            try {
                const response = await fetch('http://localhost:3333/auth/sign-in', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user: '<EMAIL>',
                        password: 'aaAA**11'
                    })
                });

                console.log('Response recebida:', response);
                console.log('Status:', response.status);
                console.log('Headers:', [...response.headers.entries()]);

                if (response.ok) {
                    const data = await response.json();
                    console.log('Dados recebidos:', data);
                    resultDiv.innerHTML = `
                        <h3>✅ Sucesso!</h3>
                        <p>Status: ${response.status}</p>
                        <p>Token recebido: ${data.access_token ? 'Sim' : 'Não'}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    const errorText = await response.text();
                    console.log('Erro HTTP:', errorText);
                    resultDiv.innerHTML = `
                        <h3>❌ Erro HTTP</h3>
                        <p>Status: ${response.status}</p>
                        <p>Status Text: ${response.statusText}</p>
                        <p>Resposta: ${errorText}</p>
                    `;
                }
            } catch (error) {
                console.error('Erro completo:', error);
                console.error('Stack trace:', error.stack);
                resultDiv.innerHTML = `
                    <h3>❌ Erro de Rede</h3>
                    <p>Erro: ${error.message}</p>
                    <p>Tipo: ${error.name}</p>
                    <p>Stack: ${error.stack}</p>
                `;
            }
        }

        // Teste com XMLHttpRequest
        function testLoginXHR() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testando com XMLHttpRequest...';

            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'http://localhost:3333/auth/sign-in', true);
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 201) {
                        const data = JSON.parse(xhr.responseText);
                        resultDiv.innerHTML = `
                            <h3>✅ Sucesso com XMLHttpRequest!</h3>
                            <p>Status: ${xhr.status}</p>
                            <p>Token recebido: ${data.access_token ? 'Sim' : 'Não'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <h3>❌ Erro XMLHttpRequest</h3>
                            <p>Status: ${xhr.status}</p>
                            <p>Response: ${xhr.responseText}</p>
                        `;
                    }
                }
            };

            xhr.onerror = function() {
                resultDiv.innerHTML = `
                    <h3>❌ Erro de Rede XMLHttpRequest</h3>
                    <p>Erro de conexão</p>
                `;
            };

            xhr.send(JSON.stringify({
                user: '<EMAIL>',
                password: 'aaAA**11'
            }));
        }

        // Teste adicional para verificar se a API está acessível
        async function testApiHealth() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testando saúde da API...';

            try {
                const response = await fetch('http://localhost:3333/', {
                    method: 'GET',
                });
                console.log('API Health Check:', response.status);
                resultDiv.innerHTML = `
                    <h3>✅ API Acessível</h3>
                    <p>Status: ${response.status}</p>
                `;
                return response.status;
            } catch (error) {
                console.error('API não acessível:', error);
                resultDiv.innerHTML = `
                    <h3>❌ API Não Acessível</h3>
                    <p>Erro: ${error.message}</p>
                `;
                return false;
            }
        }

        // Teste específico para simular o comportamento do useStoreAuth
        async function testExactAuth() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testando exatamente como o useStoreAuth...';

            console.log("🔐 Iniciando login...");
            console.log("📍 URL da API:", "http://localhost:3333");
            console.log("👤 Usuário:", "<EMAIL>");

            try {
                const apiUrl = "http://localhost:3333/auth/sign-in";
                console.log("🌐 URL completa:", apiUrl);

                const response = await fetch(apiUrl, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        user: "<EMAIL>",
                        password: "aaAA**11"
                    }),
                });

                console.log("📡 Response recebida:", response);
                console.log("📊 Status:", response.status);
                console.log("📋 Headers:", [...response.headers.entries()]);

                const data = await response.json();
                console.log("📦 Dados recebidos:", data);

                if (response.status === 201) {
                    resultDiv.innerHTML = `
                        <h3>✅ Sucesso! (Exatamente como useStoreAuth)</h3>
                        <p>Status: ${response.status}</p>
                        <p>Token recebido: ${data.access_token ? 'Sim' : 'Não'}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>❌ Erro HTTP</h3>
                        <p>Status: ${response.status}</p>
                        <p>Response: ${JSON.stringify(data, null, 2)}</p>
                    `;
                }
            } catch (error) {
                console.error("❌ Erro capturado:", error);
                console.error("❌ Tipo do erro:", error.name);
                console.error("❌ Mensagem:", error.message);
                console.error("❌ Stack:", error.stack);

                resultDiv.innerHTML = `
                    <h3>❌ Erro de Rede (Igual ao useStoreAuth)</h3>
                    <p>Erro: ${error.message}</p>
                    <p>Tipo: ${error.name}</p>
                    <p>Stack: ${error.stack}</p>
                `;
            }
        }

        // Executar teste de saúde da API ao carregar a página
        window.onload = async () => {
            const healthStatus = await testApiHealth();
            console.log('Status inicial da API:', healthStatus);
        };
    </script>
</body>
</html>
