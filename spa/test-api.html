<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste API</title>
</head>
<body>
    <h1>Teste de Conexão com a API</h1>
    <button onclick="testLogin()">Testar Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testando...';

            console.log('Iniciando teste de login...');
            console.log('URL da API:', 'http://localhost:3333/auth/sign-in');

            try {
                const response = await fetch('http://localhost:3333/auth/sign-in', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user: '<EMAIL>',
                        password: 'aaAA**11'
                    })
                });

                console.log('Response recebida:', response);
                console.log('Status:', response.status);
                console.log('Headers:', [...response.headers.entries()]);

                if (response.ok) {
                    const data = await response.json();
                    console.log('Dados recebidos:', data);
                    resultDiv.innerHTML = `
                        <h3>✅ Sucesso!</h3>
                        <p>Status: ${response.status}</p>
                        <p>Token recebido: ${data.access_token ? 'Sim' : 'Não'}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    const errorText = await response.text();
                    console.log('Erro HTTP:', errorText);
                    resultDiv.innerHTML = `
                        <h3>❌ Erro HTTP</h3>
                        <p>Status: ${response.status}</p>
                        <p>Status Text: ${response.statusText}</p>
                        <p>Resposta: ${errorText}</p>
                    `;
                }
            } catch (error) {
                console.error('Erro completo:', error);
                console.error('Stack trace:', error.stack);
                resultDiv.innerHTML = `
                    <h3>❌ Erro de Rede</h3>
                    <p>Erro: ${error.message}</p>
                    <p>Tipo: ${error.name}</p>
                    <p>Stack: ${error.stack}</p>
                `;
            }
        }

        // Teste adicional para verificar se a API está acessível
        async function testApiHealth() {
            try {
                const response = await fetch('http://localhost:3333/', {
                    method: 'GET',
                });
                console.log('API Health Check:', response.status);
                return response.status;
            } catch (error) {
                console.error('API não acessível:', error);
                return false;
            }
        }

        // Executar teste de saúde da API ao carregar a página
        window.onload = async () => {
            const healthStatus = await testApiHealth();
            const statusDiv = document.createElement('div');
            statusDiv.innerHTML = `<p>Status da API: ${healthStatus ? `HTTP ${healthStatus}` : 'Não acessível'}</p>`;
            document.body.insertBefore(statusDiv, document.getElementById('result'));
        };
    </script>
</body>
</html>
