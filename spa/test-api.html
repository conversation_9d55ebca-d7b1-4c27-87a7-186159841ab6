<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste API</title>
</head>
<body>
    <h1>Teste de Conexão com a API</h1>
    <button onclick="testLogin()">Testar Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testando...';
            
            try {
                const response = await fetch('http://localhost:3333/auth/sign-in', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user: '<EMAIL>',
                        password: 'aaAA**11'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <h3>✅ Sucesso!</h3>
                        <p>Status: ${response.status}</p>
                        <p>Token recebido: ${data.access_token ? 'Sim' : 'Não'}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>❌ Erro HTTP</h3>
                        <p>Status: ${response.status}</p>
                        <p>Status Text: ${response.statusText}</p>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>❌ Erro de Rede</h3>
                    <p>Erro: ${error.message}</p>
                    <p>Tipo: ${error.name}</p>
                `;
                console.error('Erro completo:', error);
            }
        }
    </script>
</body>
</html>
