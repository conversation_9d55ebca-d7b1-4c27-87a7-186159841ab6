@baseUrl = http://localhost:3333
@authToken = {{authenticate.response.body.access_token}}

# @name create_account
POST {{baseUrl}}/accounts
Content-Type: application/json

{
    "first_name": "<PERSON>e",
    "last_name": "<PERSON><PERSON>",
    "email": "<EMAIL>",
    "password": "ABab**12"
}


###

# @name authenticate
POST {{baseUrl}}/auth/sign-in
Content-Type: application/json

{
    "user": "<EMAIL>",
    "password": "aaAA**11"
}

###
# @name me
GET {{baseUrl}}/me
Content-Type: application/json
Authorization: Bearer {{authToken}}


###

# @name forget
POST {{baseUrl}}/auth/forget
Content-Type: application/json

{
    "email": "<EMAIL>"
}


###

# @name reset
POST {{baseUrl}}/auth/reset/0193d5c1-e0c7-778e-bd95-12ed1aefca05
Content-Type: application/json

{
    "password": "<PERSON>ab**12",
    "password_confirm": "_ABab**12"
}

