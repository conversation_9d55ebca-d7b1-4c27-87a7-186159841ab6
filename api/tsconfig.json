{"compilerOptions": {"module": "commonjs", "declaration": false, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"@/*": ["./src/*"]}, "incremental": true, "skipLibCheck": true, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "strictNullChecks": true, "strict": true, "types": ["vitest/globals"], "resolveJsonModule": true}}