## comandos

faz a indentação e mapeia os relacionamentos

```bash
npx prisma format
```

criar as tipagens

```bash
npx prisma generate
```

forçar a criação das tipagens

```bash
rm -rf node_modules/.prisma
```

criar as tipagens

```bash
npx prisma generate
```

testando a conexão

```bash
npx prisma db push
```

## migrations

criar a migration

```bash
npx prisma migrate dev --create-only --name alter_table_
```

```bash
npx prisma migrate dev
```

```bash
npx prisma migrate deploy
```

atualizar o schema

```bash
npx prisma db pull
```

remover a migration

```bash
npx prisma migrate reset
```

seeds

```bash
npx prisma db seed
```

prisma studio

```bash
npx prisma studio
```

### Configurações VSCode

ativar o `prisma format` ao salvar

```json
  "editor.formatOnSave": true,
  "[prisma]": {
    "editor.defaultFormatter": "Prisma.prisma"
  },
```

atualizar o campo moment dos incoming_messages

```sql
UPDATE incoming_messages
SET momment = (to_timestamp((payload->>'momment')::bigint / 1000.0) AT TIME ZONE 'UTC')
WHERE momment IS NULL OR momment <> (to_timestamp((payload->>'momment')::bigint / 1000.0) AT TIME ZONE 'UTC');
```

atualizar o campo momment dos sent_messages

```sql
UPDATE sent_messages sm
SET momment = (to_timestamp((im.payload->>'momment')::bigint / 1000.0) AT TIME ZONE 'UTC')
FROM incoming_messages im
WHERE sm.reference_message_id = im.message_id
  AND im.payload->>'momment' IS NOT NULL
  AND (sm.momment IS NULL OR sm.momment <> (to_timestamp((im.payload->>'momment')::bigint / 1000.0) AT TIME ZONE 'UTC'));
```

```sql
INSERT INTO "_prisma_migrations" (id,checksum,finished_at,migration_name,logs,rolled_back_at,started_at,applied_steps_count) VALUES
	 ('2f5906cb-62f1-4d68-bff1-7ed35dfc9cce','ca80bbc5af78a8de7263b5a9845c608c878fb2b29a2d62deeab39714fea29ad8','2025-05-03 20:11:02.09275-03','20250503231030_initial',NULL,NULL,'2025-05-03 20:11:01.165123-03',1);
```

operadores de intervalo

gt: greater than (maior que)

lt: less than (menor que)

lte: less than or equal to (menor ou igual a)

equals: igual a

npx prisma migrate resolve --applied 20250322123143_alter_cache

```

```

\

model IgnoredMessages {
id String @id @default(uuid())
phone String?
message_id String? @unique
message String?
status String?
note String?
payload Json
created_at DateTime @default(now())

    @@map("ignored_messages")

}
