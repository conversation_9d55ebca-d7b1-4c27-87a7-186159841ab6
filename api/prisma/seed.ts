import { PrismaClient } from "@prisma/client";
import { roles, settings, users, permissions, permission_role, role_user } from "./seeds";

export const prisma = new PrismaClient();

async function main() {
    console.log("seeding...");

    await roles();
    await users();
    await settings();
    await permissions();
    await permission_role();
    await role_user();
}

main()
    .catch((error) => {
        console.log(error);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });
