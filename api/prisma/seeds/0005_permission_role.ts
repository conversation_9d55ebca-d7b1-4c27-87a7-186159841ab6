import { prisma } from "../seed";
import { rolesIds } from "./0001_roles";
import { permissionsIds } from "./0004_permissions";

export async function permission_role() {
    console.log("permission_role");

    await prisma.permissionRole.deleteMany();

    await prisma.permissionRole.createMany({
        data: [
            {
                id: "aab56696-90a4-4b07-869d-7f051871c81c",
                permission_id: permissionsIds[0],
                role_id: rolesIds[0],
            },
            {
                id: "bcd56696-90a4-4b07-869d-7f051871c81d",
                permission_id: permissionsIds[1],
                role_id: rolesIds[0],
            },
            {
                id: "cde56696-90a4-4b07-869d-7f051871c81e",
                permission_id: permissionsIds[0],
                role_id: rolesIds[1],
            },
            {
                id: "def56696-90a4-4b07-869d-7f051871c81f",
                permission_id: permissionsIds[1],
                role_id: rolesIds[1],
            },
        ]
    });
}
