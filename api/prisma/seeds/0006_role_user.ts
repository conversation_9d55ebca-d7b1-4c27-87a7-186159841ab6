import { prisma } from "../seed";
import { rolesIds } from "./0001_roles";
import { usersIds } from "./0002_users";

export async function role_user() {
    console.log("role_user");

    await prisma.roleUser.deleteMany();

    await prisma.roleUser.createMany({
        data: [
            {
                id: "aab56696-90a4-4b07-869d-7f051871c81c",
                user_id: usersIds[0],
                role_id: rolesIds[0],
            },
        ]
    });
}
