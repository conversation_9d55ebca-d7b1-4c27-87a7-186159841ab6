import { prisma } from "../seed";


export const permissionsIds = [
    "bbe982b2-8418-484d-9bc8-b848cc003b9f",
    "aab56696-90a4-4b07-869d-7f051871c81c",
    "cbe982b2-8418-484d-9bc8-b848cc003b9f",
    "dbe982b2-8418-484d-9bc8-b848cc003b9f"
]

export async function permissions() {
    console.log("permissions");

    await prisma.permission.deleteMany();

    await prisma.permission.createMany({
        data: [
            {
                id: permissionsIds[0],
                name: "Crear usuario",
                slug: "c-users",
                description: "Permite crear un usuario"
            },
            {
                id: permissionsIds[1],
                name: "Listar usuarios",
                slug: "r-users",
                description: "Permite listar usuarios"
            },
            {
                id: permissionsIds[2],
                name: "Editar usuario",
                slug: "u-users",
                description: "Permite editar usuarios"
            },
            {
                id: permissionsIds[3],
                name: "Excluir usuario",
                slug: "d-users",
                description: "Permite excluir usuarios"
            }
        ]
    });
}
