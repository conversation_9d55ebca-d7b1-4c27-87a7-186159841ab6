import { hash, genSalt } from "bcryptjs";

import { prisma } from "../seed";

export const usersIds = [
    "ddbb04d4-deaf-4e33-af64-c49f25cf43f2",
    "aab56696-90a4-4b07-869d-7f051871c81c"
]


export async function users() {
    const salt = await genSalt(8);
    const password = await hash("aaAA**11" + salt, 8);

    console.log("usuarios");

    await prisma.user.deleteMany();


    await prisma.user.create({
        data: {
            id: usersIds[0],
            phone: "5521964276349",
            first_name: "Admin",
            last_name: "Admin",
            document: "08931946708",
            email: "<EMAIL>",
            password,
            notify: true,
            salt,
        },
    });

    await prisma.user.create({
        data: {
            id: usersIds[1],
            phone: "5521971674310",
            first_name: "User",
            last_name: "User",
            document: "73818303703",
            email: "<EMAIL>",
            password,
            notify: false,
            status: false,
            salt,
        },
    });
}
