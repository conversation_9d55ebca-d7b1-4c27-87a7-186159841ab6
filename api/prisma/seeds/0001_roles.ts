import { prisma } from "../seed";

export const rolesIds = [
    "aab56696-90a4-4b07-869d-7f051871c81c",
    "bbe982b2-8418-484d-9bc8-b848cc003b9f"
]

export async function roles() {
    console.log("roles");

    await prisma.role.deleteMany();

    await prisma.role.createMany({
        data: [
            {
                id: rolesIds[0],
                name: "Ad<PERSON>",
                slug: "admin",
            },
            {
                id: rolesIds[1],
                name: "<PERSON><PERSON>",
                slug: "manager",
            },
        ],
    });
}
