generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

model Cache {
    key        String    @id
    value      J<PERSON>
    created_at DateTime  @default(now())
    expires_at DateTime?

    @@map("_cache")
}

model User {
    id           String        @id @default(uuid())
    email        String        @unique
    document     String        @unique
    first_name   String
    last_name    String
    password     String
    phone        String        @unique
    avatar       String?
    salt         String
    role         String?
    notify       Boolean       @default(false)
    status       Boolean       @default(true)
    created_at   DateTime      @default(now())
    updated_at   DateTime?     @updatedAt
    created_by   String?
    updated_by   String?
    forgot_token ForgotToken[]
    RoleUser     RoleUser[]
    user_token   UserToken[]

    @@map("users")
}

model Role {
    id             String           @id @default(uuid())
    name           String           @unique
    slug           String           @unique
    description    String?
    status         Boolean          @default(true)
    created_at     DateTime         @default(now())
    updated_at     DateTime?        @updatedAt
    created_by     String?
    updated_by     String?
    PermissionRole PermissionRole[]
    RoleUser       RoleUser[]

    @@map("roles")
}

model RoleUser {
    id         String   @id @default(uuid())
    user_id    String
    role_id    String
    created_at DateTime @default(now())
    created_by String?
    role       Role     @relation(fields: [role_id], references: [id], onDelete: Cascade)
    user       User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

    @@unique([user_id, role_id])
    @@map("role_user")
}

model Permission {
    id             String           @id @default(uuid())
    name           String           @unique
    slug           String           @unique
    description    String?
    status         Boolean          @default(true)
    created_at     DateTime         @default(now())
    updated_at     DateTime?        @updatedAt
    created_by     String?
    updated_by     String?
    PermissionRole PermissionRole[]

    @@map("permissions")
}

model PermissionRole {
    id            String     @id @default(uuid())
    permission_id String
    role_id       String
    created_at    DateTime   @default(now())
    created_by    String?
    permission    Permission @relation(fields: [permission_id], references: [id], onDelete: Cascade)
    role          Role       @relation(fields: [role_id], references: [id], onDelete: Cascade)

    @@unique([permission_id, role_id])
    @@map("permission_role")
}

model ForgotToken {
    id         String   @id @default(uuid())
    email      String   @unique
    token      String   @unique
    user_id    String?
    expires_at DateTime
    created_at DateTime @default(now())
    user       User?    @relation(fields: [user_id], references: [id], onDelete: Cascade)

    @@map("forgot_tokens")
}

model UserToken {
    id         String   @id @default(uuid())
    user_id    String?
    email      String?
    token      String   @unique
    type       String
    expires_at DateTime
    created_at DateTime @default(now())
    user       User?    @relation(fields: [user_id], references: [id], onDelete: Cascade)

    @@map("user_tokens")
}

model Settings {
    id    String @id @default(uuid())
    extra Json?

    @@map("settings")
}
