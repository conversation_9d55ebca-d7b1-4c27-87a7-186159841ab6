import "dotenv/config";

import { PrismaClient } from "@prisma/client";
import { randomUUID } from "node:crypto";
import { execSync } from "node:child_process";
import { HttpStatus, INestApplication, ValidationPipe } from "@nestjs/common";

const prisma = new PrismaClient();

// a estrategia utilizada será criar um novo schema para cada teste e2e e depois remove-lo
function generateUniqueDatabaseURL(schemaId: string) {
    if (!process.env.DATABASE_URL) {
        throw new Error("Please provider a DATABASE_URL environment variable");
    }

    const url = new URL(process.env.DATABASE_URL);
    url.searchParams.set("schema", schemaId);

    return url.toString();
}

const schemaId = randomUUID();

beforeAll(async () => {
    const databaseURL = generateUniqueDatabaseURL(schemaId);
    process.env.DATABASE_URL = databaseURL;

    execSync("npx prisma migrate deploy");
    execSync("npx prisma db seed");
});

afterAll(async () => {
    await prisma.$executeRawUnsafe(
        `DROP SCHEMA IF EXISTS "${schemaId}" CASCADE`,
    );
    await prisma.$disconnect();
});

export const globalPipes = (app: INestApplication): void => {
    app.useGlobalPipes(
        new ValidationPipe({
            errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY, // 422
            whitelist: true,
            forbidNonWhitelisted: true,
            transform: true,
        }),
    );
};
