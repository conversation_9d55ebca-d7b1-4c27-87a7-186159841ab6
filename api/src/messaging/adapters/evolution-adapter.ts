import { Injectable, Logger } from '@nestjs/common';
import { MessagingAdapter, MessageResponse, MessageStatus, IncomingMessage } from './messaging-adapter.abstract';
import { HttpService } from '@/http/http.service';

@Injectable()
export class EvolutionApiAdapter extends MessagingAdapter {


    private readonly logger = new Logger(EvolutionApiAdapter.name);

    constructor(
        private readonly httpService: HttpService,
    ) {
        super();
    }



    async sendTextMessage(to: string, text: string): Promise<MessageResponse> {
        if (!this.validatePhoneNumber(to)) {
            throw new Error('Invalid phone number');
        }

        try {
            const response = await this.httpService.post<MessageResponse>('/messages/send', {
                number: to,
                textMessage: {
                    text
                }
            });

            return {
                id: response.data?.id!,
                status: 'sent',
                timestamp: new Date()
            };
        } catch (error) {
            this.logger.error('Error sending text message via Evolution API', error);
            throw error;
        }
    }



    sendMediaMessage(to: string, mediaUrl: string, caption?: string): Promise<MessageResponse> {
        throw new Error('Method not implemented.');
    }
    sendTemplateMessage(to: string, templateName: string, variables: Record<string, string>): Promise<MessageResponse> {
        throw new Error('Method not implemented.');
    }
    getMessageStatus(messageId: string): Promise<MessageStatus> {
        throw new Error('Method not implemented.');
    }
    handleIncomingMessage(webhookData: any): Promise<IncomingMessage> {
        throw new Error('Method not implemented.');
    }
}