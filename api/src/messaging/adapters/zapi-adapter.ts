import { Injectable, Logger } from "@nestjs/common";
import {
    MessagingAdapter,
    MessageResponse,
    MessageStatus,
    IncomingMessage,
} from "./messaging-adapter.abstract";
import { HttpService } from "@/http/http.service";

@Injectable()
export class ZapiAdapter extends MessagingAdapter {
    private readonly logger = new Logger(ZapiAdapter.name);

    constructor(private readonly httpService: HttpService) {
        super();
    }

    async sendTextMessage(to: string, text: string): Promise<MessageResponse> {
        if (!this.validatePhoneNumber(to)) {
            throw new Error("Invalid phone number");
        }

        try {
            const response = await this.httpService.post<MessageResponse>(
                "/instances/instanceId/token/text",
                {
                    phone: to,
                    message: text,
                },
            );

            if (!response.data) {
                throw new Error("No data returned from Z-API");
            }

            return {
                id: response.data.id,
                status: "sent",
                timestamp: new Date(),
            };
        } catch (error) {
            this.logger.error("Error sending text message via Z-API", error);
            throw error;
        }
    }

    async sendMediaMessage(
        to: string,
        mediaUrl: string,
        caption?: string,
    ): Promise<MessageResponse> {
        // Implementação similar para mídia
        return {} as MessageResponse;
    }

    async sendTemplateMessage(
        to: string,
        templateName: string,
        variables: Record<string, string>,
    ): Promise<MessageResponse> {
        // Implementação para templates
        return {} as MessageResponse;
    }

    async getMessageStatus(messageId: string): Promise<MessageStatus> {
        // Implementação para verificar status
        return {} as MessageStatus;
    }

    async handleIncomingMessage(webhookData: any): Promise<IncomingMessage> {
        // Implementação para processar webhook
        console.log({ webhookData });
        this.logger.log(webhookData);
        return {} as IncomingMessage;
    }
}
