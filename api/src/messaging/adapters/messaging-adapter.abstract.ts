export abstract class MessagingAdapter {
    abstract sendTextMessage(to: string, text: string): Promise<MessageResponse>;
    abstract sendMediaMessage(to: string, mediaUrl: string, caption?: string): Promise<MessageResponse>;
    abstract sendTemplateMessage(to: string, templateName: string, variables: Record<string, string>): Promise<MessageResponse>;
    abstract getMessageStatus(messageId: string): Promise<MessageStatus>;
    abstract handleIncomingMessage(webhookData: any): Promise<IncomingMessage>;

    protected validatePhoneNumber(phone: string): boolean {
        return /^[0-9]+$/.test(phone) && phone.length >= 10 && phone.length <= 15;
    }
}

export interface MessageResponse {
    id: string;
    status: 'sent' | 'delivered' | 'read' | 'failed';
    timestamp: Date;
}

export interface MessageStatus {
    id: string;
    status: 'sent' | 'delivered' | 'read' | 'failed';
    timestamp: Date;
}

export interface IncomingMessage {
    from: string;
    text?: string;
    mediaUrl?: string;
    timestamp: Date;
    messageId: string;
}