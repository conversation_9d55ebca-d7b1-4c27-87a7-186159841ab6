import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, } from '@nestjs/config';
import { MessagingService } from './messaging.service';
import { MessagingController } from './messaging.controller';
import { HttpService } from '@/http/http.service';
import { ZapiAdapter } from './adapters/zapi-adapter';
import { HttpModule } from '@/http/http.module';
//import { EvolutionApiAdapter } from './adapters/evolution-adapter';

@Module({
    imports: [ConfigModule, HttpModule],
    providers: [
        {
            provide: 'MESSAGING_ADAPTER',
            useFactory: (httpService: HttpService) => {
                return new ZapiAdapter(httpService);
            },
            inject: [HttpService]
        },
        MessagingService
    ],
    controllers: [MessagingController],
    exports: [MessagingService]
})
export class MessagingModule { }