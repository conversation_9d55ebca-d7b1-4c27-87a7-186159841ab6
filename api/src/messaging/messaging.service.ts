import { Injectable, Inject } from '@nestjs/common';
import { MessagingAdapter } from './adapters/messaging-adapter.abstract';
import { SendMessageDto } from './dtos/send-message.dto';
import { Group, MirrorGroup } from './adapters/messaging-adapter.abstract';

@Injectable()
export class MessagingService {
    constructor(
        @Inject('MESSAGING_ADAPTER')
        private readonly message: MessagingAdapter
    ) { }

    async sendTextMessage(dto: SendMessageDto) {
        return this.message.sendTextMessage(dto.to, dto.text);
    }

    async handleIncomingMessage(webhookData: any) {
        return this.message.handleIncomingMessage(webhookData);
    }

    async listGroups(): Promise<Group[]> {
        return this.message.listGroups();
    }

    async mirrorGroup(mirrorGroup: MirrorGroup): Promise<Group> {
        return this.message.mirrorGroup(mirrorGroup);
    }

    async getGroupDetails(groupId: string): Promise<Group> {
        return this.message.getGroupDetails(groupId);
    }
}