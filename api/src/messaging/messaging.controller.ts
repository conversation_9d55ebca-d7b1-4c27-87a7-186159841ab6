import { <PERSON>, Post, Body, HttpCode, Get, Param } from '@nestjs/common';
import { MessagingService } from './messaging.service';
import { SendMessageDto } from './dtos/send-message.dto';
import { MirrorGroup } from './adapters/messaging-adapter.abstract';

@Controller("messages")
export class MessagingController {
    constructor(private readonly messagingService: MessagingService) { }

    @Post('send')
    @HttpCode(200)
    async sendMessage(@Body() dto: SendMessageDto) {
        return this.messagingService.sendTextMessage(dto);
    }

    @Post('webhook')
    @HttpCode(200)
    async handleWebhook(@Body() data: any) {
        return this.messagingService.handleIncomingMessage(data);
    }

    @Get('groups')
    async listGroups() {
        return this.messagingService.listGroups();
    }

    @Post('groups/mirror')
    @HttpCode(200)
    async mirrorGroup(@Body() mirrorGroup: MirrorGroup) {
        return this.messagingService.mirrorGroup(mirrorGroup);
    }

    @Get('groups/:id')
    async getGroupDetails(@Param('id') groupId: string) {
        return this.messagingService.getGroupDetails(groupId);
    }
}