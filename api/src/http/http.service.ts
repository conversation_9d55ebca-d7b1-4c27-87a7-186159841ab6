import { Injectable } from "@nestjs/common";
import { HttpService as HttpAxiosService } from "@nestjs/axios";
import { AxiosResponse, AxiosError, AxiosRequestConfig } from "axios";
import { firstValueFrom } from "rxjs";

@Injectable()
export class HttpService {
    constructor(private readonly httpService: HttpAxiosService) {}

    async get<T>(
        url: string,
        config?: AxiosRequestConfig<any> | undefined,
    ): Promise<{ status: number; data: T | null; error?: any }> {
        try {
            const response: AxiosResponse<T> = await firstValueFrom(
                this.httpService.get<T>(url, config),
            );
            return { status: response.status, data: response.data };
        } catch (error) {
            if (error instanceof AxiosError) {
                const response = error.response as AxiosResponse<T>;
                return {
                    status: response?.status || 500,
                    data: null,
                    error: error.message,
                };
            }

            return {
                status: 500,
                data: null,
                error,
            };
        }
    }

    async post<T>(
        url: string,
        data: any,
        config?: AxiosRequestConfig<any> | undefined,
    ): Promise<{ status: number; data: T | null; error?: any }> {
        try {
            const response: AxiosResponse<T> = await firstValueFrom(
                this.httpService.post<T>(url, data, config),
            );
            return { status: response.status, data: response.data };
        } catch (error) {
            if (error instanceof AxiosError) {
                const response = error.response as AxiosResponse<T>;
                return {
                    status: response?.status || 500,
                    data: null,
                    error: error.message,
                };
            }

            return {
                status: 500,
                data: null,
                error,
            };
        }
    }

    async put<T>(
        url: string,
        data: any,
        config?: AxiosRequestConfig<any> | undefined,
    ): Promise<{ status: number; data: T | null; error?: any }> {
        try {
            const response: AxiosResponse<T> = await firstValueFrom(
                this.httpService.put<T>(url, data, config),
            );
            return { status: response.status, data: response.data };
        } catch (error) {
            if (error instanceof AxiosError) {
                const response = error.response as AxiosResponse<T>;
                return {
                    status: response?.status || 500,
                    data: null,
                    error: error.message,
                };
            }

            return {
                status: 500,
                data: null,
                error,
            };
        }
    }

    async delete<T>(
        url: string,
    ): Promise<{ status: number; data: T | null; error?: any }> {
        try {
            const response: AxiosResponse<T> = await firstValueFrom(
                this.httpService.delete<T>(url),
            );
            return { status: response.status, data: response.data };
        } catch (error) {
            if (error instanceof AxiosError) {
                const response = error.response as AxiosResponse<T>;
                return {
                    status: response?.status || 500,
                    data: null,
                    error: error.message,
                };
            }

            return {
                status: 500,
                data: null,
                error,
            };
        }
    }
}
