import { Module } from "@nestjs/common";
import { HttpModule as HttpAxiosModule } from "@nestjs/axios";
import { HttpService } from "./http.service";

@Module({
    imports: [
        HttpAxiosModule.registerAsync({
            useFactory: async () => ({
                timeout: 9000,
                headers: {
                    withCredentials: true,
                    accessControlAllowOrigin: "*",
                    accessControlAllowHeader:
                        "Origin, X-Requested-With, Content-Type, Accept",
                    "Content-Type": "application/json",
                },
            }),
        }),
    ],
    providers: [HttpService],
    exports: [HttpService],
})
export class HttpModule {}
