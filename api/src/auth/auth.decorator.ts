import { ExecutionContext, createParamDecorator } from "@nestjs/common";
import { User } from "@prisma/client";

export interface AuthData extends User {
    acl: string[];
}

/**
 * Return user authenticated
 * @returns AuthData
 * @example
 * async store(@Auth() auth: AuthData) {
 *  console.log(auth.id); // 555f9c62-4fe9-4aec-a173-3f49593a1ffb
 *  return { auth };
 * }
 */
export const Auth = createParamDecorator<ExecutionContext>((_, ctx) => {
    const request = ctx.switchToHttp().getRequest();

    if (!request.auth) {
        throw new Error(
            "O decorador Auth deve ser usado após o AuthGuard. Certifique se você aplicou o @UseGuards(AuthGuard) anteriormente.",
        );
    }

    return { ...request.auth.user };
});
