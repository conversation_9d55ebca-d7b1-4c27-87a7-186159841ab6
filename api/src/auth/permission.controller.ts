import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    UseGuards,
} from "@nestjs/common";
import { PermissionService } from "./permission.service";
import { AuthGuard, Can } from "./auth.guard";
import { Auth } from "./auth.decorator";
import { ThrottlerGuard } from "@nestjs/throttler";
import { AuthTesteDTO } from "./dto/auth-teste.dto";
import { PermissionCreateDTO } from "./dto/permission-create.dto";
import { acl } from "@/enums";

@Controller("auth")
@UseGuards(AuthGuard)
@Can(acl.admin)
export class PermissionController {
    constructor(private readonly service: PermissionService) {}

    @Get("permissions")
    @UseGuards(AuthGuard)
    async permissions() {
        return this.service.fetchAllPermissions();
    }

    @Post("permissions")
    @UseGuards(AuthGuard)
    async createPermission(@Body() dto: PermissionCreateDTO) {
        return this.service.createPermission(dto);
    }

    @Put("permissions/:id")
    @UseGuards(AuthGuard)
    async updatePermission(
        @Param("id") id: string,
        @Body() dto: PermissionCreateDTO,
    ) {
        return this.service.updatePermission(id, dto);
    }

    @Delete("permissions/:id")
    @UseGuards(AuthGuard)
    async deletePermission(@Param("id") id: string) {
        return this.service.deletePermission(id);
    }

    @Post("teste")
    @UseGuards(ThrottlerGuard, AuthGuard)
    async teste(@Auth() auth: AuthTesteDTO) {
        return { auth };
    }
}
