import { SetMetadata, applyDecorators, UseGuards } from "@nestjs/common";
import { AuthGuard } from "./auth.guard"; // Importe o AuthGuard
import { acl } from "@/enums";

/**
 *
 * @param acl enum
 * @example
 *  \@Can(acl.r_user) // r-user (❗admin tem acesso a todos os recursos use "acl.adm" apenas se você desejar tornar explicito)
 *  \@Get()
 */
export const Can = (...acl: acl[]) => {
    return applyDecorators(UseGuards(AuthGuard), SetMetadata("acl", acl));
};
