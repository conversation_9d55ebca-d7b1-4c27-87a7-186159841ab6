import {
    CanActivate,
    ExecutionContext,
    ForbiddenException,
    Injectable,
    UnauthorizedException,
} from "@nestjs/common";
import { AuthService } from "./auth.service";
import { UsersService } from "@/users/users.service";
import { Reflector } from "@nestjs/core";

export * from "./can.decorator";

@Injectable()
export class AuthGuard implements CanActivate {
    constructor(
        private readonly authService: AuthService,
        private readonly userService: UsersService,
        private readonly reflect: Reflector,
    ) {}

    async canActivate(context: ExecutionContext) {
        const acl = this.reflect.getAllAndOverride<string[]>("acl", [
            context.getHandler(),
            context.getClass(),
        ]);
        const request = context.switchToHttp().getRequest();
        const { authorization } = request.headers;

        if (!authorization) {
            return false;
        }

        try {
            const token = authorization.replace("Bearer ", "");
            const data = this.authService.checkToken(token, "access");
            const user = await this.userService.findMe(data.sub);
            request.auth = { user };

            if (!acl || ["admin"].some((slug) => user.acl.includes(slug))) {
                return true;
            }

            if (!acl.some((slug) => user.acl.includes(slug))) {
                throw new ForbiddenException(
                    "você não tem permissão para acessar este recurso",
                );
            }

            return true;
        } catch (error) {
            if (
                error instanceof UnauthorizedException ||
                error instanceof ForbiddenException
            ) {
                throw error;
            }

            return false;
        }
    }
}
