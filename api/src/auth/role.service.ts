import { PrismaService } from "@/prisma/prisma.service";
import { BadRequestException, Injectable } from "@nestjs/common";
import { RoleCreateDTO } from "./dto/role-create.dto";

@Injectable()
export class RoleService {
    constructor(private readonly prisma: PrismaService) {}

    async fetchAllRoles() {
        return this.prisma.role.findMany();
    }

    async getRoleById(id: string) {
        return this.prisma.role.findUnique({
            where: { id },
        });
    }

    async fetchAllPermissions() {
        const permission = await this.prisma.permission.findMany({
            include: {
                PermissionRole: {
                    select: {
                        role: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        });

        return permission.map((permission) => {
            const { PermissionRole, ...rest } = permission;
            return {
                ...rest,
                roles: PermissionRole.map((role) => role.role),
            };
        });
    }

    async createRole(data: RoleCreateDTO) {
        await this.checkRoleIsUnique(data);
        return await this.prisma.role.create({
            data,
        });
    }

    async updateRole(id: string, data: RoleCreateDTO) {
        await this.checkRoleIsUnique(data, id);
        return await this.prisma.role.update({
            where: { id },
            data,
        });
    }

    async deleteRole(id: string) {
        return await this.prisma.role.delete({
            where: { id },
        });
    }

    // privates methods \\
    private async checkRoleIsUnique(data: RoleCreateDTO, id?: string) {
        const { name, slug } = data;
        await this.prisma.role
            .findFirst({
                where: {
                    name,
                    ...(id && { id: { not: id } }),
                },
            })
            .then((result) => {
                if (result) {
                    throw new BadRequestException(
                        "O nome informado já está em uso",
                    );
                }
            });
        await this.prisma.role
            .findFirst({
                where: {
                    slug,
                    ...(id && { id: { not: id } }),
                },
            })
            .then((result) => {
                if (result) {
                    throw new BadRequestException(
                        "O slug informado já está em uso",
                    );
                }
            });
    }
}
