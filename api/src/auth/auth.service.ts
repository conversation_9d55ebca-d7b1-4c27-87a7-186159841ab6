import { PrismaService } from "@/prisma/prisma.service";
import {
    BadRequestException,
    Injectable,
    UnauthorizedException,
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { User } from "@prisma/client";
import { hash, compare } from "bcryptjs";
import { EnvService } from "@/env";
import { MailService } from "@/mail/mail.service";
import { v7 as uuidv7 } from "uuid";
import { RoleCreateDTO } from "./dto/role-create.dto";

@Injectable()
export class AuthService {
    constructor(
        private readonly jwtService: JwtService,
        private readonly prisma: PrismaService,
        private readonly env: EnvService,
        private readonly mailService: MailService,
    ) {}

    createToken(user: User) {
        const access_token = this.jwtService.sign(
            { sub: user.id, type: "access" },
            {
                expiresIn: this.env.get("JWT_EXPIRES_IN"),
            },
        );
        const refresh_token = this.jwtService.sign(
            { sub: user.id, type: "refresh" },
            {
                expiresIn: this.env.get("JWT_REFRESH_EXPIRES_IN"),
            },
        );

        return {
            access_token,
            refresh_token,
        };
    }

    checkToken(token: string, type: "refresh" | "access"): { sub: string } {
        try {
            const payload = this.jwtService.verify(token);

            if (type && payload.type !== type) {
                throw new UnauthorizedException(
                    `Invalid ${payload.type} token`,
                );
            }

            return payload;
        } catch (error) {
            throw new UnauthorizedException(error); // 401
        }
    }

    async refreshToken(refresh_token: string) {
        const { sub } = this.checkToken(refresh_token, "refresh");

        const user = await this.prisma.user.findUniqueOrThrow({
            where: { id: sub, status: true },
        });

        if (!sub) {
            throw new UnauthorizedException("Invalid refresh token");
        }

        return this.createToken(user);
    }

    async login(user: string, password: string) {
        const userExists = await this.prisma.user.findUnique({
            where: {
                email: user,
            },
        });

        if (!userExists) {
            throw new BadRequestException("Usuário e/ou senha inválidos");
        }
        const checkPassword = await compare(
            password + userExists.salt,
            userExists.password,
        );

        if (!checkPassword) {
            throw new BadRequestException("Usuário e/ou senha inválidos");
        }

        return this.createToken(userExists);
    }

    async forget(email: string): Promise<{ message: string }> {
        let forgotToken = await this.prisma.forgotToken.findUnique({
            where: { email },
        });

        if (forgotToken && forgotToken.expires_at > new Date()) {
            return {
                message:
                    "Você já solicitou uma recuperação de senha. Por favor, aguarde 30 minutos para solicitar uma nova recuperação de senha.",
            };
        }

        await this.prisma.forgotToken.deleteMany({
            where: {
                expires_at: {
                    lte: new Date(),
                },
            },
        });

        const user = await this.prisma.user.findUnique({
            where: { email },
        });

        forgotToken = await this.prisma.forgotToken.create({
            data: {
                email,
                user_id: user ? user.id : null,
                token: uuidv7(),
                expires_at: new Date(Date.now() + 10 * 60 * 1000), // 10 minutos
            },
        });

        if (!user) {
            return {
                message:
                    "Se você possui uma conta, um email foi enviado para você com as instruções para redefinir sua senha.",
            };
        }

        await this.mailService.sendTokenForget(user, forgotToken.token);

        return {
            message:
                "Se você possui uma conta, um email foi enviado para você com as instruções para redefinir sua senha.",
        };
    }

    async newPass(token: string, password: string) {
        const userToken = await this.prisma.userToken.findUnique({
            where: {
                token,
                type: "NEW_PASSWORD",
                expires_at: {
                    gt: new Date(), // gt=greater than (mair que a data atual)
                },
            },
            include: { user: true },
        });
        if (!userToken) {
            throw new BadRequestException("Token inválido ou expirado");
        }

        const hashedPassword = await hash(password + userToken.user?.salt, 8);
        await this.prisma.user.update({
            where: { id: userToken.user_id! },
            data: { password: hashedPassword },
        });

        await this.prisma.userToken.deleteMany({
            where: {
                OR: [{ token }, { expires_at: { lte: new Date() } }],
            },
        });

        await this.mailService.sendPasswordChanged(userToken.user!);

        return {
            message: "Senha alterada com sucesso",
        };
    }

    async reset(token: string, password: string) {
        const forgotToken = await this.prisma.forgotToken.findUnique({
            where: {
                token,
                user_id: { not: null },
                expires_at: {
                    gt: new Date(), // gt=greater than (mair que a data atual)
                },
            },
            include: { user: true },
        });
        if (!forgotToken) {
            throw new BadRequestException("Token inválido ou expirado");
        }

        const hashedPassword = await hash(password + forgotToken.user?.salt, 8);
        await this.prisma.user.update({
            where: { id: forgotToken.user_id! },
            data: { password: hashedPassword },
        });

        await this.prisma.forgotToken.deleteMany({
            where: {
                OR: [{ token }, { expires_at: { lte: new Date() } }],
            },
        });

        await this.mailService.sendPasswordChanged(forgotToken.user!);

        return {
            message: "Senha alterada com sucesso",
        };
    }

    async fetchAllRoles() {
        return this.prisma.role.findMany();
    }

    async getRoleById(id: string) {
        return this.prisma.role.findUnique({
            where: { id },
        });
    }

    async fetchAllPermissions() {
        const permission = await this.prisma.permission.findMany({
            include: {
                PermissionRole: {
                    select: {
                        role: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        });

        return permission.map((permission) => {
            const { PermissionRole, ...rest } = permission;
            return {
                ...rest,
                roles: PermissionRole.map((role) => role.role),
            };
        });
    }

    async createRole(data: RoleCreateDTO) {
        await this.checkRoleIsUnique(data);
        return await this.prisma.role.create({
            data,
        });
    }

    async updateRole(id: string, data: RoleCreateDTO) {
        await this.checkRoleIsUnique(data, id);
        return await this.prisma.role.update({
            where: { id },
            data,
        });
    }

    async deleteRole(id: string) {
        return await this.prisma.role.delete({
            where: { id },
        });
    }

    // privates methods \\
    private async checkRoleIsUnique(data: RoleCreateDTO, id?: string) {
        const { name, slug } = data;
        await this.prisma.role
            .findFirst({
                where: {
                    name,
                    ...(id && { id: { not: id } }),
                },
            })
            .then((result) => {
                if (result) {
                    throw new BadRequestException(
                        "O nome informado já está em uso",
                    );
                }
            });
        await this.prisma.role
            .findFirst({
                where: {
                    slug,
                    ...(id && { id: { not: id } }),
                },
            })
            .then((result) => {
                if (result) {
                    throw new BadRequestException(
                        "O slug informado já está em uso",
                    );
                }
            });
    }
}
