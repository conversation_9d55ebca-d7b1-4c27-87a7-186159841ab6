import {
    <PERSON><PERSON><PERSON>y,
    IsBoolean,
    IsNot<PERSON>mpty,
    IsO<PERSON>al,
    IsString,
    IsU<PERSON><PERSON>,
} from "class-validator";

export class PermissionCreateDTO {
    @IsNotEmpty()
    @IsString()
    name!: string;

    @IsNotEmpty()
    @IsString()
    slug!: string;

    @IsString()
    @IsOptional()
    description!: string;

    @IsArray()
    @IsUUID("4", { each: true })
    roles!: string[];

    @IsBoolean()
    status!: boolean;

    @IsBoolean()
    @IsOptional()
    helper_crud!: boolean;
}
