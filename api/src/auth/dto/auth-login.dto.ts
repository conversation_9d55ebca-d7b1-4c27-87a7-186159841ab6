import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";

export class AuthLoginDTO {
    @IsNotEmpty()
    @IsString()
    @IsEmail()
    user!: string;

    @MinLength(8, {
        message:
            "A senha deve conter no mínimo $constraint1 caracteres, mas você digitou uma senha com $value caracteres",
    })
    @IsNotEmpty({ message: "Senha é obrigatória" })
    @IsString({ message: "Senha deve ser uma string" })
    password!: string;
}
