import request from "supertest";
import { AppModule } from "@/app.module";
import { Test } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import { PrismaService } from "@/prisma/prisma.service";
import { globalPipes } from "test/setup-e2e";

describe("Auth (E2E)", () => {
    let app: INestApplication;
    let prisma: PrismaService;

    const EMAIL = "<EMAIL>";
    const PASSWORD = "aaAA**11";

    beforeAll(async () => {
        const moduleRef = await Test.createTestingModule({
            imports: [AppModule],
        }).compile();

        app = moduleRef.createNestApplication();
        prisma = moduleRef.get(PrismaService);

        globalPipes(app); // Utilize quando for validar o DTO

        await app.init();
    });

    test("[POST] /auth/login", async () => {
        const response = await request(app.getHttpServer())
            .post("/auth/login")
            .send({
                user: EMAIL,
                password: PASSWORD,
            });

        // console.log("response bdy=> ", response.body);
        // console.log("response sc=> ", response.statusCode);
        expect(response.statusCode).toBe(201);
        expect(response.body).toHaveProperty("access_token");
        expect(response.body).toHaveProperty("refresh_token");
    });

    test("[POST] /auth/forget", async () => {
        it("should create a forgot token but not user", async () => {
            const _email = "<EMAIL>";

            const response = await request(app.getHttpServer())
                .post("/auth/forget")
                .send({ _email });

            expect(response.statusCode).toBe(200);
            expect(response.body).toHaveProperty("message");

            const forgotToken = await prisma.forgotToken.findUnique({
                where: { email: _email },
                include: { user: true },
            });

            expect(forgotToken).toBeTruthy();
            expect(forgotToken?.email).toBe(_email);
            expect(forgotToken?.user).toBeFalsy();
        });
        it("should create a forgot token witch user", async () => {
            const response = await request(app.getHttpServer())
                .post("/auth/forget")
                .send({ EMAIL });

            expect(response.statusCode).toBe(200);
            expect(response.body).toHaveProperty("message");

            const forgotToken = await prisma.forgotToken.findUnique({
                where: { email: EMAIL },
                include: { user: true },
            });

            expect(forgotToken).toBeTruthy();
            expect(forgotToken?.email).toBe(EMAIL);
            expect(forgotToken?.user).toBeTruthy();
        });
    });

    test("[POST] /auth/reset", async () => {
        const user = await prisma.user.findUniqueOrThrow({
            where: { email: EMAIL },
        });

        await prisma.forgotToken.create({
            data: {
                email: EMAIL,
                user_id: user.id,
                token: "token",
                expires_at: new Date(Date.now() + 10 * 60 * 1000), // 10 minutos
            },
        });

        let response = await request(app.getHttpServer())
            .post("/auth/reset/token")
            .send({
                password: "novaSenha@123",
                password_confirm: "novaSenha@123",
            });

        expect(response.statusCode).toBe(201);
        expect(response.body).toMatchObject({
            message: "Senha alterada com sucesso",
        });

        response = await request(app.getHttpServer()).post("/auth/login").send({
            user: EMAIL,
            password: "novaSenha@123",
        });

        expect(response.statusCode).toBe(201);
        expect(response.body).toHaveProperty("access_token");
    });
});
