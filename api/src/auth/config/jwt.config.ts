import { registerAs } from "@nestjs/config";
import { JwtModuleOptions } from "@nestjs/jwt";

export default registerAs(
    "jwt",
    (): JwtModuleOptions => ({
        privateKey: Buffer.from(process.env.JWT_PRIVATE_KEY || "", "base64"),
        publicKey: Buffer.from(process.env.JWT_PUBLIC_KEY || "", "base64"),

        signOptions: {
            algorithm: "RS256",
            expiresIn: process.env.JWT_EXPIRES_IN || "1h",
            issuer: process.env.APP_NAME || "apiNest",
        },
    }),
);
