import { PrismaService } from "@/prisma/prisma.service";
import { BadRequestException, Injectable } from "@nestjs/common";
import { PermissionCreateDTO } from "./dto/permission-create.dto";

@Injectable()
export class PermissionService {
    constructor(private readonly prisma: PrismaService) {}

    async fetchAllPermissions() {
        const permission = await this.prisma.permission.findMany({
            include: {
                PermissionRole: {
                    select: {
                        role: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        });

        return permission.map((permission) => {
            const { PermissionRole, ...rest } = permission;
            return {
                ...rest,
                roles: PermissionRole.map((role) => role.role),
            };
        });
    }

    async createPermission(dto: PermissionCreateDTO) {
        const { roles, helper_crud, description, ...data } = dto;

        await this.checkPermissionIsUnique(dto);

        // helper-crud
        if (helper_crud) {
            const name = data.name.split(" ")[1].toLowerCase();
            const slug = data.slug.split("-")[1].toLowerCase();

            await this.prisma.permission.deleteMany({
                where: {
                    name: { endsWith: slug },
                },
            });

            const permissionsData = [
                { name: `Criar ${name}`, slug: `c-${slug}`, description },
                { name: `Listar ${name}`, slug: `r-${slug}`, description },
                { name: `Editar ${name}`, slug: `u-${slug}`, description },
                { name: `Excluir ${name}`, slug: `d-${slug}`, description },
            ];

            const permissions = await Promise.all(
                permissionsData.map((data) =>
                    this.prisma.permission.create({ data }),
                ),
            );

            for (const permission of permissions) {
                await this.prisma.permissionRole.createMany({
                    data: roles.map((roleId) => ({
                        permission_id: permission.id,
                        role_id: roleId,
                    })),
                });
            }

            return permissions;
        }

        return await this.prisma.$transaction(async (prisma) => {
            const permission = await prisma.permission.create({
                data: {
                    ...data,
                    description,
                },
            });

            await prisma.permissionRole.createMany({
                data: roles.map((roleId) => ({
                    permission_id: permission.id,
                    role_id: roleId,
                })),
            });

            return permission;
        });
    }

    async updatePermission(id: string, dto: PermissionCreateDTO) {
        const { roles, name, slug, description, status } = dto;
        await this.checkPermissionIsUnique(dto, id);
        return await this.prisma.$transaction(async (prisma) => {
            const permission = await prisma.permission.update({
                where: { id },
                data: {
                    name,
                    slug,
                    description,
                    status,
                },
            });
            await prisma.permissionRole.deleteMany({
                where: {
                    permission_id: id,
                },
            });
            await prisma.permissionRole.createMany({
                data: roles.map((roleId) => ({
                    permission_id: id,
                    role_id: roleId,
                })),
            });
            return permission;
        });
    }

    async deletePermission(id: string) {
        return await this.prisma.permission.delete({
            where: { id },
        });
    }

    // PRIVATES METHODS \\
    private async checkPermissionIsUnique(
        data: PermissionCreateDTO,
        id?: string,
    ) {
        const { name, slug } = data;

        await this.prisma.permission
            .findFirst({
                where: {
                    name,
                    ...(id && { id: { not: id } }),
                },
            })
            .then((result) => {
                if (result) {
                    throw new BadRequestException(
                        "O nome informado já está em uso",
                    );
                }
            });
        await this.prisma.permission
            .findFirst({
                where: {
                    slug,
                    ...(id && { id: { not: id } }),
                },
            })
            .then((result) => {
                if (result) {
                    throw new BadRequestException(
                        "O slug informado já está em uso",
                    );
                }
            });
    }
}
