import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    UseGuards,
} from "@nestjs/common";
import { RoleService } from "./role.service";
import { AuthGuard, Can } from "./auth.guard";
import { RoleCreateDTO } from "./dto/role-create.dto";
import { acl } from "@/enums";

@Controller("auth")
@UseGuards(AuthGuard)
@Can(acl.admin)
export class RoleController {
    constructor(private readonly service: RoleService) {}

    @Get("roles")
    @UseGuards(AuthGuard)
    async roles() {
        return this.service.fetchAllRoles();
    }

    @Post("roles")
    @UseGuards(AuthGuard)
    async createRole(@Body() dto: RoleCreateDTO) {
        return this.service.createRole(dto);
    }

    @Put("roles/:id")
    @UseGuards(AuthGuard)
    async updateRole(@Param("id") id: string, @Body() dto: RoleCreateDTO) {
        return this.service.updateRole(id, dto);
    }

    @Delete("roles/:id")
    @UseGuards(AuthGuard)
    async deleteRole(@Param("id") id: string) {
        return this.service.deleteRole(id);
    }
}
