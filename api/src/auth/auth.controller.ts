import {
    Body,
    Controller,
    HttpStatus,
    Post,
    Res,
    UseGuards,
} from "@nestjs/common";
import { AuthLoginDTO } from "./dto/auth-login.dto";
import { AuthForgetDTO } from "./dto/auth-forget.dto";
import { AuthResetDTO } from "./dto/auth-reset.dto";
import { AuthService } from "./auth.service";
import { AuthGuard } from "./auth.guard";
import { Auth } from "./auth.decorator";
import { Throttle, ThrottlerGuard } from "@nestjs/throttler";
import { AuthTesteDTO } from "./dto/auth-teste.dto";
import { Response } from "express";
import { AuthRefreshDTO } from "./dto/auth-refresh.dto";

@Controller("auth")
@UseGuards(ThrottlerGuard)
export class AuthController {
    constructor(private readonly authService: AuthService) {}

    @Post("sign-in")
    @Throttle({ default: { limit: 3, ttl: 60000 } })
    async login(@Body() { user, password }: AuthLoginDTO) {
        return this.authService.login(user, password);
    }

    @Post("forgot")
    async forget(@Body() { email }: AuthForgetDTO, @Res() res: Response) {
        return res
            .status(HttpStatus.OK)
            .json(await this.authService.forget(email));
    }

    @Post("reset")
    async reset(@Body() { token, password }: AuthResetDTO) {
        return this.authService.reset(token, password);
    }

    @Post("new-password")
    async newPass(@Body() { token, password }: AuthResetDTO) {
        return this.authService.newPass(token, password);
    }

    @Post("refresh")
    async refresh(@Body() { refresh_token }: AuthRefreshDTO) {
        return this.authService.refreshToken(refresh_token);
    }

    @Post("teste")
    @UseGuards(ThrottlerGuard, AuthGuard)
    async teste(@Auth() auth: AuthTesteDTO) {
        return { auth };
    }
}
