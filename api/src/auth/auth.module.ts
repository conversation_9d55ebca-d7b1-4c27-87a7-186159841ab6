import { Global, Module } from "@nestjs/common";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { EnvService } from "@/env/env.service";
import { EnvModule } from "@/env/env.module";
import { AuthController } from "./auth.controller";
import { AuthService } from "./auth.service";
import { MailModule } from "@/mail/mail.module";
import { AuthGuard } from "./auth.guard";
import { PermissionService } from "./permission.service";
import { PermissionController } from "./permission.controller";
import { RoleService } from "./role.service";
import { RoleController } from "./role.controller";
import { UsersModule } from "@/users/users.module";

@Global()
@Module({
    imports: [
        UsersModule,
        PassportModule,
        JwtModule.registerAsync({
            imports: [EnvModule],
            inject: [EnvService],
            global: true,
            useFactory(env: EnvService) {
                const privateKey = env.get("JWT_PRIVATE_KEY");
                const publicKey = env.get("JWT_PUBLIC_KEY");

                return {
                    privateKey: Buffer.from(privateKey, "base64"),
                    publicKey: Buffer.from(publicKey, "base64"),

                    signOptions: {
                        algorithm: "RS256",
                        expiresIn: "1h",
                        issuer: env.get("APP_NAME"),
                    },
                };
            },
        }),
        MailModule,
    ],
    providers: [
        EnvService,
        AuthService,
        AuthGuard,
        PermissionService,
        RoleService,
    ],
    controllers: [AuthController, PermissionController, RoleController],
    exports: [AuthService, AuthGuard],
})
export class AuthModule {}
