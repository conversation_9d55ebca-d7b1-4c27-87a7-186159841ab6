import {
    Controller,
    Get,
    Post,
    Put,
    Delete,
    Body,
    Param,
    UseGuards,
} from "@nestjs/common";
import { UsersService } from "./users.service";
import { AuthGuard, Can } from "@/auth/auth.guard";
import { CreateUserDto } from "./dto/create-user.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { Auth, AuthData } from "@/auth/auth.decorator";
import { AuthResetDTO } from "@/auth/dto/auth-reset.dto";
import { acl } from "@/enums";

@Controller("users")
@UseGuards(AuthGuard)
export class UsersController {
    constructor(private readonly usersService: UsersService) {}

    @Get()
    findAll() {
        return this.usersService.findAll();
    }

    @Get(":id")
    findOne(@Param("id") id: string) {
        return this.usersService.findById(id);
    }

    @Can(acl.c_user)
    @Post()
    async create(@Body() user: CreateUserDto) {
        return this.usersService.create(user);
    }

    @Put("change-password")
    changePassword(@Auth() auth: AuthData, @Body() { password }: AuthResetDTO) {
        return this.usersService.updatePassword(auth.id, password);
    }

    @Put("profile")
    profile(@Auth() auth: AuthData, @Body() user: UpdateUserDto) {
        return this.usersService.update(auth.id, user);
    }

    @Can(acl.u_user)
    @Put(":id")
    update(@Param("id") id: string, @Body() user: UpdateUserDto) {
        return this.usersService.update(id, user);
    }

    @Can(acl.d_user)
    @Delete(":id")
    delete(@Param("id") id: string, @Auth() auth: AuthData) {
        return this.usersService.delete(id, auth.id);
    }
}
