import { Global, Module } from "@nestjs/common";
import { UsersService } from "./users.service";
import { UsersController } from "./users.controller";
import { MeController } from "./me.controller";
import { MeService } from "./me.service";
import { MailModule } from "@/mail/mail.module";

@Global()
@Module({
    imports: [MailModule],
    providers: [UsersService, MeService],
    controllers: [UsersController, MeController],
    exports: [UsersService],
})
export class UsersModule {}
