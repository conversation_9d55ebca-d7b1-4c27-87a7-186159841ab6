import {
    <PERSON>String,
    IsEmail,
    IsOptional,
    IsBoolean,
    IsNotEmpty,
    IsArray,
    IsUUID,
} from "class-validator";

export class CreateUserDto {
    @IsEmail()
    @IsNotEmpty()
    email!: string;

    @IsString()
    @IsNotEmpty()
    document!: string;

    @IsString()
    @IsNotEmpty()
    first_name!: string;

    @IsString()
    @IsNotEmpty()
    last_name!: string;

    @IsString()
    @IsNotEmpty()
    phone!: string;

    @IsOptional()
    @IsString()
    avatar?: string;

    @IsArray()
    @IsUUID("4", { each: true })
    @IsOptional()
    roles!: string[];

    @IsBoolean()
    @IsOptional()
    status?: boolean;

    @IsBoolean()
    @IsOptional()
    notify?: boolean;
}
