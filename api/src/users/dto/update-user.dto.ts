import {
    <PERSON><PERSON><PERSON>,
    Is<PERSON><PERSON>,
    Is<PERSON><PERSON>al,
    IsB<PERSON>ean,
    <PERSON><PERSON><PERSON><PERSON>,
    IsArray,
    IsU<PERSON>D,
} from "class-validator";

export class UpdateUserDto {
    @IsOptional()
    @IsString()
    first_name?: string;

    @IsOptional()
    @IsString()
    last_name?: string;

    @IsOptional()
    @IsEmail()
    email?: string;

    @IsOptional()
    @IsString()
    document?: string;

    @IsOptional()
    @IsString()
    @MinLength(8)
    password?: string;

    @IsOptional()
    @IsString()
    phone?: string;

    @IsOptional()
    @IsString()
    avatar?: string;

    @IsArray()
    @IsUUID("4", { each: true })
    @IsOptional()
    roles!: string[];

    @IsOptional()
    @IsBoolean()
    status?: boolean;

    @IsOptional()
    @IsBoolean()
    notify?: boolean;
}
