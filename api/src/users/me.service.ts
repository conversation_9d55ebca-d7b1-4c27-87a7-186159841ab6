import { Injectable } from "@nestjs/common";
import { PrismaService } from "@/prisma/prisma.service";
import { CacheService } from "@/cache/cache.service";
import { User } from "@prisma/client";

type FindMeData = User & { acl: string[] };

@Injectable()
export class MeService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly cache: CacheService,
    ) {}

    async findMe(user_id: string): Promise<FindMeData> {
        const cache = await this.cache.get(`user:${user_id}`);
        if (cache) {
            return cache as FindMeData;
        }

        const user = await this.prisma.user.findUniqueOrThrow({
            where: { id: user_id },
            select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                phone: true,
                avatar: true,
                document: true,
                status: true,
                notify: true,
            },
        });

        const slugs = await this.prisma.$queryRaw<{ slugs: string }[]>`
        /*busca os slugs dos cargos e permissões do usuário*/
        SELECT r.slug as slugs
        FROM role_user ru
        JOIN roles r ON ru.role_id = r.id
        WHERE ru.user_id = ${user_id}
        UNION
        SELECT p.slug as slugs
        FROM role_user ru
        JOIN permission_role pr ON ru.role_id = pr.role_id
        JOIN permissions p ON pr.permission_id = p.id
        WHERE ru.user_id = ${user_id}
        `;

        const value = {
            ...user,
            acl: slugs.map((slug) => slug.slugs),
        };

        await this.cache.set({
            key: `user:${user_id}`,
            value,
            ttl: 60 * 10 * 1000, // 10 minutos
        });

        return value as FindMeData;
    }
}
