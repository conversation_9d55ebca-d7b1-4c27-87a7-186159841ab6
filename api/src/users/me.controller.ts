import { Controller, Get, UseGuards } from "@nestjs/common";
import { MeService } from "./me.service";
import { AuthGuard } from "@/auth/auth.guard";
import { Auth, AuthData } from "@/auth/auth.decorator";

@Controller("me")
@UseGuards(AuthGuard)
export class MeController {
    constructor(private readonly service: MeService) {}

    @Get()
    findAll(@Auth() auth: AuthData) {
        return this.service.findMe(auth.id);
    }
}
