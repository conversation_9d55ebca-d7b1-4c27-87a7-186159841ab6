import { BadRequestException, Injectable } from "@nestjs/common";
import { PrismaService } from "@/prisma/prisma.service";
import { CreateUserDto } from "./dto/create-user.dto";
import { randomUUID } from "node:crypto";
import { genSalt, hash } from "bcryptjs";
import { UpdateUserDto } from "./dto/update-user.dto";
import { CacheService } from "@/cache/cache.service";
import { MeService } from "./me.service";
import { MailService } from "@/mail/mail.service";

@Injectable()
export class UsersService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly cache: CacheService,
        private readonly meService: MeService,
        private readonly mailService: MailService,
    ) {}

    async findAll() {
        const users = await this.prisma.user.findMany({
            include: {
                RoleUser: {
                    select: {
                        role: { select: { id: true, name: true } },
                    },
                },
            },
            orderBy: {
                first_name: "asc",
            },
        });

        return users.map((user) => ({
            id: user.id,
            first_name: user.first_name,
            last_name: user.last_name,
            email: user.email,
            phone: user.phone,
            status: user.status,
            notify: user.notify,
            document: user.document,
            roles: user.RoleUser.map((role) => ({
                id: role.role.id,
                name: role.role.name,
            })),
        }));
    }

    async findById(id: string) {
        const user = await this.prisma.user.findUniqueOrThrow({
            where: { id },
            include: {
                RoleUser: {
                    select: {
                        role: true,
                    },
                },
            },
        });

        return {
            id: user.id,
            first_name: user.first_name,
            last_name: user.last_name,
            email: user.email,
            phone: user.phone,
            status: user.status,
            document: user.document,
            notify: user.notify,
            roles: user?.RoleUser.map((role) => ({
                id: role.role.id,
                name: role.role.name,
                slug: role.role.slug,
            })),
        };
    }

    async create(dto: CreateUserDto) {
        await this.verifyIfUserUnique(dto);

        const salt = await genSalt(8);
        const password = await hash(randomUUID() + salt, 8);
        const { roles, ...data } = dto;

        const user = await this.prisma.user.create({
            data: { ...data, id: randomUUID(), salt, password },
        });

        if (roles) {
            await this.prisma.roleUser.createMany({
                data: roles.map((roleId) => ({
                    user_id: user.id,
                    role_id: roleId,
                })),
            });
        }

        await this.mailService.sendWelcomeUser(user);

        return this.findById(user.id);
    }

    async update(id: string, dto: UpdateUserDto) {
        await this.verifyIfUserUnique(dto, id);
        const { roles, ...data } = dto;

        await this.cache.delete(`users:${id}`);

        await this.prisma.user.update({
            where: { id },
            data,
        });

        await this.prisma.roleUser.deleteMany({
            where: { user_id: id },
        });

        if (roles) {
            await this.prisma.roleUser.createMany({
                data: roles.map((roleId) => ({
                    user_id: id,
                    role_id: roleId,
                })),
            });
        }

        return this.findById(id);
    }

    async delete(id: string, auth_user_id: string) {
        if (process.env.NODE_ENV !== "development") {
            throw new BadRequestException("Você não pode excluir");
        }

        if (id === auth_user_id) {
            throw new BadRequestException("Vou não pode deletar sua conta");
        }

        await this.prisma.user.findUniqueOrThrow({
            where: { id },
        });

        await this.cache.delete(`users:${id}`);

        return this.prisma.user.delete({
            where: { id },
        });
    }

    async updatePassword(id: string, password: string) {
        const salt = await genSalt(8);
        const hashPassword = await hash(password + salt, 8);
        const user = await this.prisma.user.findUniqueOrThrow({
            where: { id },
        });

        await this.prisma.user.update({
            where: { id },
            data: { password: hashPassword, salt },
        });

        await this.mailService.sendPasswordChanged(user);

        return {
            message: "Password updated successfully",
        };
    }

    async findMe(id: string) {
        return this.meService.findMe(id);
    }

    // PRIVATE METHODS
    private async verifyIfUserUnique(
        dto: CreateUserDto | UpdateUserDto,
        id?: string,
    ): Promise<void> {
        const { email, document } = dto;

        await this.prisma.user
            .findFirst({
                where: {
                    email,
                    ...(id && { id: { not: id } }),
                },
            })
            .then((user) => {
                if (user) {
                    throw new BadRequestException(
                        "o email informado já está em uso",
                    );
                }
            });

        await this.prisma.user
            .findFirst({
                where: {
                    document,
                    ...(id && { id: { not: id } }),
                },
            })
            .then((user) => {
                if (user) {
                    throw new BadRequestException(
                        "o documento informado já está em uso",
                    );
                }
            });
    }
}
