import { EnvService } from "@/env";
import { Injectable, Inject } from "@nestjs/common";
import { WINSTON_MODULE_PROVIDER } from "nest-winston";
import { Logger } from "winston";

@Injectable()
export class LoggerService {
    private isLoggingEnabled: boolean;

    constructor(
        private readonly envService: EnvService,
        @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    ) {
        this.isLoggingEnabled =
            this.envService.get("ENABLE_LOGGING") === "true";
    }

    info(message: string, object?: object) {
        if (this.isLoggingEnabled) {
            this.logger.info(message, object);
        }
    }

    log(message: string, object?: object) {
        this.logger.info(message, object);
    }

    error(message: string, object?: object) {
        if (this.isLoggingEnabled) {
            this.logger.error(message, object);
        }
    }

    warn(message: string, object?: object) {
        if (this.isLoggingEnabled) {
            this.logger.warn(message, object);
        }
    }

    debug(message: string, object?: object) {
        if (this.isLoggingEnabled) {
            this.logger.debug(message, object);
        }
    }

    verbose(message: string, object?: object) {
        if (this.isLoggingEnabled) {
            this.logger.verbose(message, object);
        }
    }
}
