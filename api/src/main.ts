import { NestFactory } from "@nestjs/core";
import { AppModule } from "@/app.module";
import {
    HttpStatus,
    ValidationPipe,
    BadRequestException,
} from "@nestjs/common";
import { EnvService } from "@/env";
import { ValidationError } from "class-validator";

async function bootstrap() {
    const app = await NestFactory.create(AppModule, {
        // logger: false,
    });

    const envService = app.get(EnvService);

    app.enableCors({
        methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
        origin: envService.get("APP_CORS").split(","),
    });

    app.useGlobalPipes(
        new ValidationPipe({
            // whitelist: true, // remove valores enviados que não estão no DTO
            // stopAtFirstError: true, // para na primeira validação que falhar
            errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY, // 422
            exceptionFactory: (errors: ValidationError[]) => {
                const result = errors?.map((error) => ({
                    field: error.property,
                    error: error?.constraints
                        ? Object.values(error.constraints).join(", ")
                        : [],
                }));
                return new BadRequestException({
                    statusCode: HttpStatus.UNPROCESSABLE_ENTITY,
                    message: result,
                });
            },
        }),
    );

    const port = envService.get("PORT");

    await app.listen(port);
}
bootstrap();
