import { Injectable } from "@nestjs/common";
import { PrismaService } from "@/prisma/prisma.service";
import { cacheConfig } from "./cache.config";

interface SetPros {
    key: string;
    value: Record<string, any>;
    ttl?: number;
}

@Injectable()
export class CacheService {
    constructor(private readonly prisma: PrismaService) {}

    async get<T>(key: string): Promise<T | null> {
        const [cache] = await this.prisma.$queryRaw<{ value: JSON }[]>`
        /** busca o cache */
        SELECT value
        FROM _cache
        WHERE key = ${key}
        AND (expires_at IS NULL OR expires_at > NOW());
        `;

        return cache ? (cache.value as T) : null;
    }

    /**
     * set cache
     * @param key - chave do cache
     * @param value - valor do cache
     * @param ttl - tempo de expiração do cache em milissegundos
     * @example set({ key: 'user:1', value: { id: 1, name: '<PERSON>' }, ttl: 60*5*1000 }) // 5 minuto
     */
    async set({ key, value, ttl }: SetPros): Promise<void> {
        const expires_at = ttl
            ? new Date(Date.now() + ttl)
            : new Date(Date.now() + cacheConfig.defaultTTL);

        await this.prisma.cache.upsert({
            where: { key },
            create: {
                key,
                value,
                expires_at,
            },
            update: {
                value,
                expires_at,
            },
        });
    }

    async delete(key: string): Promise<void> {
        await this.prisma.cache.deleteMany({ where: { key } });
    }

    async cleanupExpiredCache() {
        await this.prisma.cache.deleteMany({
            where: { expires_at: { lte: new Date() } },
        });
    }
}
