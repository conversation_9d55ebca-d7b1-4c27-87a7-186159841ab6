import { Injectable, Logger } from "@nestjs/common";
import { <PERSON><PERSON> } from "@nestjs/schedule";
import { CacheService } from "./cache.service";
import { cacheConfig } from "./cache.config";

@Injectable()
export class CacheTasks {
    private readonly logger = new Logger(CacheTasks.name);

    constructor(private readonly cacheService: CacheService) {}

    @Cron(cacheConfig.cronTime)
    async handleCleanupExpiredCache() {
        this.logger.debug("Executando limpeza de cache expirado...");
        await this.cacheService.cleanupExpiredCache();
    }
}
