export const cacheConfig = {
    defaultTTL: 60 * 10 * 1000,
    cronTime: "0 0 * * * *",
};

/** CRON
        * * * * * *
        | | | | | |
        | | | | | +-- <PERSON><PERSON> da <PERSON> (0 - 7) (0 ou 7 = <PERSON>)
        | | | | +---- <PERSON><PERSON><PERSON> (1 - 12)
        | | | +------ <PERSON><PERSON> <PERSON> m<PERSON> (1 - 31)
        | | +-------- <PERSON><PERSON> (0 - 23)
        | +---------- <PERSON><PERSON> (0 - 59)
        +------------ <PERSON><PERSON><PERSON> (0 - 59)
     */
// @<PERSON><PERSON>('0 */30 * * * *') // Executa a cada 30 minutos
// @<PERSON>ron('0 0 0 * * *') // Executa à meia-noite
// @<PERSON><PERSON>('0 0 */2 * * *') // Executa a cada 2 horas
// @Cron('0 0 8 * * 1-5') // Executa de segunda a sexta, às 8h
