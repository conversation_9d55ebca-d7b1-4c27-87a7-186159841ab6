import { SESClient } from "@aws-sdk/client-ses";
import { TransportType } from "@nestjs-modules/mailer/dist/interfaces/mailer-options.interface";
import { EnvService } from "@/env";

export const getTransport = (env: EnvService): TransportType => {
    if (env.get("MAIL_DRIVER") === "ses") {
        return {
            SES: {
                ses: new SESClient({
                    region: env.get("AWS_REGION"),
                    credentials: {
                        accessKeyId: env.get("AWS_ACCESS_KEY_ID"),
                        secretAccessKey: env.get("AWS_SECRET_ACCESS_KEY"),
                    },
                }),
                aws: require("@aws-sdk/client-ses"),
            },
        };
    }

    return {
        host: env.get("MAIL_HOST"),
        port: +env.get("MAIL_PORT"),
        auth: {
            user: env.get("MAIL_USER"),
            pass: env.get("MAIL_PASS"),
        },
    };
};
