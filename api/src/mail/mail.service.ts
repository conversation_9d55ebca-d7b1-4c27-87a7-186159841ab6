import { Injectable } from "@nestjs/common";
import { MailerService } from "@nestjs-modules/mailer";
import { SentMessageInfo } from "nodemailer";
import { User } from "@prisma/client";
import { EnvService } from "@/env";
import { PrismaService } from "@/prisma/prisma.service";
import { v7 as uuidv7 } from "uuid";

@Injectable()
export class MailService {
    constructor(
        private readonly mailerService: MailerService,
        private readonly env: EnvService,
        private readonly prisma: PrismaService,
    ) {}

    async sendMail(to: string, subject: string, text: string) {
        const mailOptions = {
            to,
            subject,
            text,
        };
        try {
            await this.mailerService.sendMail(mailOptions);
            return true;
        } catch (error) {
            throw new Error("Failed to send email");
        }
    }

    async sendWelcomeUser(user: User): Promise<SentMessageInfo> {
        const token = await this.createNewPasswordToken(user);
        const url = `${this.env.get("APP_URL")}/auth/new-password/${token}`;

        return this.mailerService.sendMail({
            to: user.email!,
            subject: "Seja bem-vindo",
            template: "./welcome",
            context: {
                title: "Seja bem-vindo",
                name: user.first_name,
                url,
            },
        });
    }

    async sendTokenForget(user: User, token: string): Promise<SentMessageInfo> {
        const url = `${this.env.get("APP_URL")}/auth/reset/${token}`;
        return this.mailerService.sendMail({
            to: user.email!,
            subject: "Recuperação de senha",
            template: "./forget",
            context: {
                title: "Recuperação de senha",
                name: user.first_name,
                url,
            },
        });
    }

    async sendPasswordChanged(user: User): Promise<SentMessageInfo> {
        return this.mailerService.sendMail({
            to: user.email!,
            subject: "Senha Alterada",
            template: "./password-changed",
            context: {
                title: "Senha alterada",
                name: user.first_name,
            },
        });
    }

    private async createNewPasswordToken(user: User): Promise<string> {
        const token = await this.prisma.userToken.create({
            data: {
                token: uuidv7(),
                user_id: user.id,
                email: user.email,
                type: "NEW_PASSWORD",
                expires_at: new Date(Date.now() + 1000 * 60 * 60 * 24), // 1dia
            },
        });

        return token.token;
    }
}
