import { describe, it, expect, beforeEach, vi } from "vitest";
import { MailerService } from "@nestjs-modules/mailer";
import { EnvService } from "@/env";
import { MailService } from "./mail.service";

describe("MailService", () => {
    let envService: EnvService;
    let mailerService: MailerService;
    let service: MailService;

    beforeEach(() => {
        service = new MailService(mailerService, envService);
    });

    it("should be defined", () => {
        expect(service).toBeDefined();
    });

    it("should send an email successfully", async () => {
        const sendMailMock = vi.fn().mockResolvedValue(true);
        service.sendMail = sendMailMock;

        const result = await service.sendMail(
            "<EMAIL>",
            "Test Email",
            "This is a test email.",
        );

        expect(sendMailMock).toHaveBeenCalled();
        expect(result).toBe(true);
    });

    it("should handle email sending failure", async () => {
        const sendMailMock = vi
            .fn()
            .mockRejectedValue(new Error("Failed to send email"));
        service.sendMail = sendMailMock;

        await expect(
            service.sendMail(
                "<EMAIL>",
                "Test Email",
                "This is a test email.",
            ),
        ).rejects.toThrow("Failed to send email");
    });
});
