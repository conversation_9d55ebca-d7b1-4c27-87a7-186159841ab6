import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { MailService } from "./mail.service";
import { MailerModule } from "@nestjs-modules/mailer";
import { HandlebarsAdapter } from "@nestjs-modules/mailer/dist/adapters/handlebars.adapter";
import { EnvModule, EnvService } from "@/env";
import { resolve } from "node:path";
import { getTransport } from "./config/transport.config";

@Module({
    imports: [
        MailerModule.forRootAsync({
            imports: [EnvModule],
            inject: [EnvService],
            useFactory: (env: EnvService) => ({
                defaults: {
                    from: env.get("MAIL_FROM"),
                },
                transport: getTransport(env),
                preview: env.get("NODE_ENV") === "development",
                template: {
                    dir: resolve(__dirname, "templates", "pages"),
                    adapter: new HandlebarsAdapter(),
                    options: {
                        strict: true,
                    },
                },
                options: {
                    partials: {
                        dir: resolve(__dirname, "templates", "partials"),
                        options: {
                            strict: true,
                        },
                    },
                },
            }),
        }),
        EnvModule,
    ],
    providers: [MailService],
    exports: [MailService],
})
export class MailModule {}
