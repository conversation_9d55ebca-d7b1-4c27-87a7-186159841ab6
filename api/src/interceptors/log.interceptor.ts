import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, NestInterceptor } from "@nestjs/common";
import { Observable } from "rxjs";
import { tap } from "rxjs/operators";

export class LogInterceptor implements NestInterceptor {
    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const now = Date.now();
        return next
            .handle()
            .pipe(
                tap(() =>
                    console.log(
                        `tempo de execução: ${context.getClass().name}.${
                            context.getHandler().name
                        } - ${Date.now() - now}ms`,
                        context.getClass().name,
                    ),
                ),
            );
    }
}
