import { z } from "zod";

export const envSchema = z.object({
    APP_NAME: z.string(),
    APP_CORS: z.string().default("*"),
    APP_URL: z.string(),
    API_URL: z.string(),
    DATABASE_URL: z.string(),
    PORT: z.coerce.number().optional().default(3333),
    JWT_PRIVATE_KEY: z.string(),
    JWT_PUBLIC_KEY: z.string(),
    JWT_EXPIRES_IN: z.string().default("1h"),
    JWT_REFRESH_EXPIRES_IN: z.string().default("90m"), // Expira em 90 minutos
    THROTTLE_TTL: z.coerce.number().optional().default(60000),
    THROTTLE_LIMIT: z.coerce.number().optional().default(60),
    MAIL_DRIVER: z.string().default("smtp"),
    MAIL_HOST: z.string(), // smtps://<EMAIL>:<EMAIL>
    MAIL_PORT: z.coerce.number().optional().default(2525),
    MAIL_USER: z.string(),
    MAIL_PASS: z.string(),
    MAIL_FROM: z.string(), // "No Reply <<EMAIL>>"
    NODE_ENV: z.string().default("development"),
    AWS_REGION: z.string().default("us-east-1 "),
    AWS_ACCESS_KEY_ID: z.string(),
    AWS_SECRET_ACCESS_KEY: z.string(),
    ENABLE_LOGGING: z.string(),
    API_SMS_URL: z.string(),
    API_SMS_AUTH: z.string(), // echo -n usuario:senha | base64
});

export type Env = z.infer<typeof envSchema>;
