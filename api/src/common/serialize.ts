type Serializable = Record<string, unknown>;

export const serialize = {
    pick<T extends Serializable, K extends keyof T>(
        obj: T | null,
        attributesToPick: K[],
    ): Pick<T, K> {
        if (!obj) {
            return {} as Pick<T, K>;
        }
        const pickedObj: Pick<T, K> = {} as Pick<T, K>;

        attributesToPick.forEach((attribute) => {
            if (Object.prototype.hasOwnProperty.call(obj, attribute)) {
                pickedObj[attribute] = obj[attribute];
            }
        });

        return pickedObj;
    },

    omit<T extends Serializable, K extends keyof T>(
        obj: T | null,
        attributesToRemove: K[],
    ): Omit<T, K> {
        if (!obj) {
            return {} as Omit<T, K>;
        }

        const serializedObj = { ...obj };

        attributesToRemove.forEach((attribute) => {
            if (
                Object.prototype.hasOwnProperty.call(serializedObj, attribute)
            ) {
                delete serializedObj[attribute];
            }
        });

        return serializedObj as Omit<T, K>;
    },
};
