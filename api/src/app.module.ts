import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { AuthModule } from "@/auth/auth.module";
import { envSchema, EnvModule, EnvService } from "@/env";
import { ThrottlerModule } from "@nestjs/throttler";
import { PrismaModule } from "@/prisma/prisma.module";
import { MailModule } from "@/mail/mail.module";
import { UsersModule } from "@/users/users.module";
import { HttpModule } from "@/http/http.module";
import { LoggerModule } from "@/logger/logger.module";
import { CacheModule } from "./cache/cache.module";
import { ScheduleModule } from "@nestjs/schedule";
import { MessagingModule } from "./messaging/messaging.module";

@Module({
    imports: [
        ConfigModule.forRoot({
            validate: (env) => envSchema.parse(env),
            isGlobal: true,
        }),
        EnvModule,
        ThrottlerModule.forRootAsync({
            imports: [EnvModule],
            inject: [EnvService],
            useFactory: (env: EnvService) => [
                {
                    ttl: +env.get("THROTTLE_TTL"), // milliseconds
                    limit: +env.get("THROTTLE_LIMIT"),
                },
            ],
        }),
        CacheModule,
        AuthModule,
        UsersModule,
        PrismaModule,
        MailModule,
        HttpModule,
        LoggerModule,
        ScheduleModule.forRoot(),
        MessagingModule
    ],
    controllers: [],
    providers: [EnvService],
})
export class AppModule { }
