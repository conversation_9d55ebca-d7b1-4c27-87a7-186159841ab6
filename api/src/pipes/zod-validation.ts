import { Pipe<PERSON>ransform, BadRequestException } from "@nestjs/common";
import { Zod<PERSON>rror, ZodSchema } from "zod";
import { fromZodError } from "zod-validation-error";

export class ZodValidationPipe implements PipeTransform {
    constructor(private schema: ZodSchema) {}

    transform(value: unknown) {
        try {
            return this.schema.parse(value);
        } catch (error) {
            if (error instanceof ZodError) {
                throw new BadRequestException({
                    message: "Validation failed",
                    statusCode: 400,
                    errors: fromZodError(error),
                });
            }

            throw new BadRequestException("Validation failed");
        }
    }
}
