instalar os pacotes 
```bash
npm install
```

gerando as chave publica e privada jtw

```bash
openssl genrsa -out private-key.pem 2048
cat private-key.pem
openssl rsa -in private-key.pem -pubout -out public-key.pem
cat public-key.pem
```

converter a chave pública para base64
```bash
cat private-key.pem | base64 > private-key-b64.txt
cat public-key.pem | base64 > public-key-b64.txt
```

alterar as variáveis de ambiente no arquivo .env com as chaves geradas em base64
```env
JWT_PRIVATE_KEY=LS0tLS1CRUdJTiBSU0EgUFJJVkFURSBLRVktLS0tLQpNSU...
JWT_PUBLIC_KEY=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5...
```


Banco de dados Postgress .env `mirror`
```
DATABASE_URL="postgresql://docker:docker@localhost:5432/mirror?schema=public"
```

testando a conexão

```bash
npx prisma db push
```

```bash
npx prisma migrate dev
```

```bash
npx prisma db seed
```