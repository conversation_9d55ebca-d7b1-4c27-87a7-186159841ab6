import swc from "unplugin-swc";
import { defineConfig } from "vitest/config";
import tsConfigPaths from "vite-tsconfig-paths";

export default defineConfig({
    test: {
        include: ["src/**/*.e2e-spec.ts"],
        globals: true,
        root: "./",
        setupFiles: ["./test/setup-e2e.ts"], // define o arquivo de setup para os testes e2e o qual será chamando antes de rodar um teste
    },
    plugins: [
        tsConfigPaths(),
        swc.vite({
            module: { type: "es6" },
        }),
    ],
});
