{"name": "api-pergamo", "version": "0.2.1", "description": "", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "vitest run", "test:watch": "vitest", "test:cov": "vitest run --coverage", "test:debug": "vitest --inspect-brk --inspect --logHeapUsage --threads=false", "test:e2e": "vitest run --config ./vitest.config.e2e.mjs"}, "dependencies": {"@aws-sdk/client-ses": "^3.713.0", "@faker-js/faker": "^8.3.1", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^5.0.1", "@nestjs/throttler": "^5.1.0", "@prisma/client": "^6.5.0", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "class-validator-translator-middleware": "^1.0.5", "enforce-unique": "^1.2.0", "handlebars": "^4.7.8", "nest-winston": "^1.10.2", "nodemailer": "^6.9.16", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "uuid": "^11.0.3", "winston": "^3.17.0", "zod": "^3.22.4", "zod-validation-error": "^2.1.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@rocketseat/eslint-config": "^2.1.0", "@swc/core": "^1.3.99", "@types/bcryptjs": "^2.4.6", "@types/express": "^4.17.17", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^3.0.13", "@types/supertest": "^2.0.16", "@vitest/coverage-v8": "^0.34.6", "dotenv": "^16.3.1", "eslint": "^8.54.0", "prisma": "^6.5.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "unplugin-swc": "^1.4.3", "vite-tsconfig-paths": "^4.2.1", "vitest": "^0.34.6"}, "prisma": {"schema": "./prisma/schema.prisma", "seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}